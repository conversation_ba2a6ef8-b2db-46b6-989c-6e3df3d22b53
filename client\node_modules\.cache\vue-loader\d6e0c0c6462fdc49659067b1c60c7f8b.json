{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue?vue&type=template&id=0b639959&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue", "mtime": 1748971860662}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}