{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue", "mtime": 1748959578563}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RootCauseChart.vue"], "names": [], "mappings": ";AAQA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "RootCauseChart.vue", "sourceRoot": "src/components/RootCauseChart", "sourcesContent": ["\n\n<template>\n  <div ref='chartHolder'>\n  </div>\n</template>\n\n<script>\nimport Vue from 'vue';\nimport '@carbon/charts/styles.css'; // Import Carbon Charts styles\nimport chartsVue from '@carbon/charts-vue';\nimport { StackedBarChart } from '@carbon/charts'; // Import StackedBarChart\n\nVue.use(chartsVue);\n\nexport default {\n  name: 'RootCauseChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: true // Default is loading true\n    },\n    title: {\n      type: String,\n      default: 'Root Cause Categories'\n    },\n    eventType: {\n      type: Function,\n      default: () => {}\n    }\n  },\n  data() {\n    return {\n      options:{\n      title: this.title,\n        axes: {\n          left: {\n            title: 'Fail Rate (%)',\n            mapsTo: 'value',\n            stacked: true,\n            \n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        toolbar: {\n          enabled: true\n        },\n        tooltip: {\n          enabled: true\n        },\n        height: this.height,\n        theme: 'g100',\n        stacked: true,\n        animations: true,\n        bars: {\n          width: 50\n        },\n        data: {\n          // groupMapsTo: 'group',\n          loading: this.loading\n        }\n      }\n    }\n    },\n  mounted() {\n    const chartHolder = this.$refs.chartHolder;\n    console.log(chartHolder);\n\n    this.chart = new StackedBarChart(chartHolder, {\n      data: this.data,\n      options: this.options,\n    });\n\n    this.chart.services.events.addEventListener('bar-click', (e) => {\n      this.eventType(e);\n    });\n  },\n  watch: {\n    loading(newVal) {\n      // Update loading state dynamically when the prop changes\n      this.options.data.loading = newVal;\n      this.chart.model.setOptions(this.options);\n    },\n    data(newData) {\n        this.chart.model.setData(newData);\n      \n    }\n  },\n};\n</script>\n\n<style>\nbutton[aria-label=\"Show as table\"] {\n  display: none;\n  pointer-events: none;\n}\ndiv.toolbar-control.bx--overflow-menu[aria-label=\"Show as table\"] {\n  display: none;\n}\n</style>\n"]}]}