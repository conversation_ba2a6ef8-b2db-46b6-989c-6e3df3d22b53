{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue", "mtime": 1748971860662}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DefectValidations.vue"], "names": [], "mappings": ";AAiWA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "DefectValidations.vue", "sourceRoot": "src/views/DefectValidations", "sourcesContent": ["<template>\r\n  <div class=\"validations-container\">\r\n    <!-- Inherit the MainHeader component -->\r\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" />\r\n\r\n    <!-- Page Header -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">Defect Validations</h1>\r\n      <div class=\"header-actions\">\r\n        <cv-button kind=\"primary\" @click=\"refreshData\">Refresh Data</cv-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Filter Bar -->\r\n    <div class=\"filter-bar\">\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">PQE Owner:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedPQEOwner\"\r\n          label=\"Filter by PQE Owner\"\r\n          :items=\"pqeOwnerOptions\"\r\n          @change=\"handlePQEOwnerChange\"\r\n        />\r\n      </div>\r\n\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">Process/Commodity:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedProcess\"\r\n          label=\"Filter by process\"\r\n          :items=\"processOptions\"\r\n          @change=\"loadValidationData\"\r\n        ></cv-dropdown>\r\n      </div>\r\n\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">Time Period:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedTimeRange\"\r\n          label=\"Filter by time period\"\r\n          :items=\"rangeOptions\"\r\n          @change=\"loadValidationData\"\r\n        ></cv-dropdown>\r\n      </div>\r\n\r\n      <div class=\"search-box\">\r\n        <cv-search\r\n          v-model=\"searchQuery\"\r\n          label=\"Search\"\r\n          placeholder=\"Search by part number or group...\"\r\n          @input=\"filterValidations\"\r\n        ></cv-search>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div v-if=\"isLoading\" class=\"loading-container\">\r\n      <cv-loading :active=\"true\" :small=\"false\" :withOverlay=\"false\" />\r\n      <p class=\"loading-text\">Loading validation data...</p>\r\n    </div>\r\n\r\n    <!-- Error State -->\r\n    <div v-else-if=\"loadingError\" class=\"error-container\">\r\n      <cv-inline-notification\r\n        kind=\"error\"\r\n        :title=\"'Error'\"\r\n        :sub-title=\"loadingError\"\r\n      />\r\n    </div>\r\n\r\n    <!-- Content when data is loaded -->\r\n    <div v-else class=\"validations-content\">\r\n      <!-- Summary Tiles -->\r\n      <div class=\"validations-summary\">\r\n      \r\n\r\n        <cv-tile class=\"summary-tile\">\r\n          <h4 class=\"tile-title\">Validated</h4>\r\n          <p class=\"tile-value validated\">{{ validationsSummary.validated }}</p>\r\n          <p class=\"tile-percentage\">{{ validationsSummary.validatedPercentage }}%</p>\r\n        </cv-tile>\r\n\r\n        <cv-tile class=\"summary-tile\">\r\n          <h4 class=\"tile-title\">Unvalidated</h4>\r\n          <p class=\"tile-value unvalidated\">{{ validationsSummary.unvalidated }}</p>\r\n          <p class=\"tile-percentage\">{{ validationsSummary.unvalidatedPercentage }}%</p>\r\n        </cv-tile>\r\n      </div>\r\n\r\n      <!-- Main Content (removed tabs) -->\r\n      <div class=\"main-validation-content\">\r\n        <!-- PQE Validation Overview Section -->\r\n        <div class=\"validation-section\" v-if=\"selectedPQEOwner && selectedPQEOwner !== 'All'\">\r\n          <h3>PQE Validation Overview - {{ selectedPQEOwner }}</h3>\r\n\r\n          <!-- Validation Chart -->\r\n          <div class=\"chart-container\">\r\n            <div class=\"chart-wrapper\">\r\n              <GaugeChart v-if=\"pqeValidationData.total > 0\" :data=\"pqeChartData\" />\r\n              <div v-else class=\"no-data-message\">\r\n                <p>No validation data available for {{ selectedPQEOwner }}</p>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Validation Stats -->\r\n            <div class=\"validation-stats\">\r\n              <div class=\"stat-item\">\r\n                <span class=\"stat-label\">Total Fails:</span>\r\n                <span class=\"stat-value\">{{ pqeValidationData.total }}</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <span class=\"stat-label\">Validated:</span>\r\n                <span class=\"stat-value validated\">{{ pqeValidationData.validated }}</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <span class=\"stat-label\">Unvalidated:</span>\r\n                <span class=\"stat-value unvalidated\">{{ pqeValidationData.unvalidated }}</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <span class=\"stat-label\">Validation Rate:</span>\r\n                <span class=\"stat-value\">{{ pqeValidationData.percentage }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Part Numbers Summary -->\r\n          <div class=\"part-numbers-summary\" v-if=\"pqePartNumbers.length > 0\">\r\n            <h4>Part Numbers ({{ pqePartNumbers.length }} total)</h4>\r\n            <div class=\"part-numbers-grid\">\r\n              <cv-tag\r\n                v-for=\"pn in pqePartNumbers.slice(0, 20)\"\r\n                :key=\"pn\"\r\n                kind=\"gray\"\r\n                class=\"part-number-tag\"\r\n              >\r\n                {{ pn }}\r\n              </cv-tag>\r\n              <cv-tag\r\n                v-if=\"pqePartNumbers.length > 20\"\r\n                kind=\"blue\"\r\n                class=\"part-number-tag\"\r\n              >\r\n                +{{ pqePartNumbers.length - 20 }} more\r\n              </cv-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Default message when no PQE selected -->\r\n        <div class=\"validation-section\" v-else>\r\n          <h3>PQE Validation Overview</h3>\r\n          <div class=\"empty-state\">\r\n            <p>Please select a PQE Owner to view validation data.</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Legacy View Section -->\r\n        <div class=\"validation-section legacy-view\">\r\n          <h3>Legacy Validation View</h3>\r\n          <cv-content-switcher aria-label=\"Choose content\" @selected='reset()'>\r\n            <cv-content-switcher-button content-selector=\".content-1\" :selected=\"selectedIndex === 0\">Cables</cv-content-switcher-button>\r\n            <cv-content-switcher-button content-selector=\".content-2\" :selected=\"selectedIndex === 1\">Power/Thermal</cv-content-switcher-button>\r\n          </cv-content-switcher>\r\n\r\n          <div class=\"progress-section content-1\">\r\n            <cv-progress>\r\n              <cv-progress-step\r\n                v-for=\"(step, index) in cableStepsStatus\"\r\n                :key=\"index\"\r\n                :label=\"step.label\"\r\n                :complete=\"step.complete\"\r\n                @step-clicked=\"stepClick(step.pn, step.label)\"\r\n              ></cv-progress-step>\r\n            </cv-progress>\r\n          </div>\r\n\r\n          <div class=\"progress-section content-2\">\r\n            <cv-progress>\r\n              <cv-progress-step\r\n                v-for=\"(step, index) in powerThermalSteps\"\r\n                :key=\"index\"\r\n                :label=\"step.label\"\r\n                :complete=\"step.complete\"\r\n                @step-clicked=\"stepClick(step.pn, step.label)\"\r\n              ></cv-progress-step>\r\n            </cv-progress>\r\n          </div>\r\n\r\n          <h3>{{ clickedStepName }}</h3>\r\n\r\n          <div v-if=\"stepClicked\">\r\n            <cv-dropdown\r\n              label=\"Range of Fails\"\r\n              v-model=\"selectedRange\"\r\n              :items=\"rangeOptions\"\r\n              :selected-item=\"selectedRange\"\r\n            ></cv-dropdown>\r\n\r\n            <GaugeChart v-if=\"gaugeActive\" :data=\"gaugeData\" />\r\n\r\n            <div class=\"fail-container\">\r\n              <p><strong>Unvalidated count:</strong> {{ unvalidated_count }}</p>\r\n              <p><strong>Total number of Fails:</strong> {{ total_fails }}</p>\r\n\r\n              <!-- Buttons under the bar chart -->\r\n              <div class=\"button-container\">\r\n                <cv-button @click=\"viewData\">View Data</cv-button>\r\n                <cv-button @click=\"validateEach\">Validate Each</cv-button>\r\n                <cv-button @click=\"validateBulk\">Validate Bulk</cv-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Validation Details Modal -->\r\n    <cv-modal\r\n      class=\"validation-details-modal\"\r\n      :visible=\"detailsModalVisible\"\r\n      @modal-hidden=\"detailsModalVisible = false\"\r\n      :size=\"'lg'\"\r\n    >\r\n      <template slot=\"title\">\r\n        <div>{{ selectedItem ? (selectedItem.partNumber ? 'Part Number: ' + selectedItem.partNumber : 'Group: ' + selectedItem.name) : 'Validation Details' }}</div>\r\n      </template>\r\n      <template slot=\"content\">\r\n        <div class=\"modal-content\" v-if=\"selectedItem\">\r\n          <!-- Status Banner -->\r\n          <div class=\"status-banner status-banner-in-progress\">\r\n            <div class=\"validation-summary\">\r\n              <span class=\"validation-label\">Validation Rate:</span>\r\n              <cv-tag :kind=\"getValidationTagKind(selectedItem.percentage)\">\r\n                {{ selectedItem.percentage }}%\r\n              </cv-tag>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Validation Details -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Validation Summary</div>\r\n            <div class=\"info-grid\">\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Total Validations</span>\r\n                <span class=\"info-value\">{{ selectedItem.total }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Validated</span>\r\n                <span class=\"info-value\">{{ selectedItem.validated }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Unvalidated</span>\r\n                <span class=\"info-value\">{{ selectedItem.unvalidated }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Time Period</span>\r\n                <span class=\"info-value\">{{ selectedTimeRange }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Validation Items Table -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Validation Items</div>\r\n            <cv-data-table\r\n              :columns=\"validationDetailsColumns\"\r\n              :pagination=\"{ pageSize: 10 }\"\r\n              :title=\"''\"\r\n            >\r\n              <template slot=\"data\">\r\n                <cv-data-table-row\r\n                  v-for=\"(item, index) in selectedItemDetails\"\r\n                  :key=\"index\"\r\n                >\r\n                  <cv-data-table-cell>{{ item.defect_id }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.pn }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.sn }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ formatDate(item.date) }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>\r\n                    <cv-tag :kind=\"item.status === 'validated' ? 'green' : 'red'\">\r\n                      {{ item.status === 'validated' ? 'Validated' : 'Unvalidated' }}\r\n                    </cv-tag>\r\n                  </cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.root_cause_1 || 'N/A' }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>\r\n                    <cv-button\r\n                      kind=\"ghost\"\r\n                      size=\"small\"\r\n                      @click=\"validateItem(item)\"\r\n                      v-if=\"item.status !== 'validated'\"\r\n                    >\r\n                      Validate\r\n                    </cv-button>\r\n                    <span v-else>-</span>\r\n                  </cv-data-table-cell>\r\n                </cv-data-table-row>\r\n\r\n                <!-- Empty state -->\r\n                <cv-data-table-row v-if=\"selectedItemDetails.length === 0\">\r\n                  <cv-data-table-cell colspan=\"7\" class=\"empty-message\">\r\n                    No validation items found.\r\n                  </cv-data-table-cell>\r\n                </cv-data-table-row>\r\n              </template>\r\n            </cv-data-table>\r\n          </div>\r\n\r\n          <!-- Bulk Validation Section -->\r\n          <div class=\"modal-section\" v-if=\"hasUnvalidatedItems\">\r\n            <div class=\"section-title\">Bulk Validation</div>\r\n            <div class=\"bulk-validation-form\">\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Root Cause</label>\r\n                <cv-dropdown\r\n                  v-model=\"bulkValidationRootCause\"\r\n                  label=\"Select Root Cause\"\r\n                  :items=\"rootCauseOptions\"\r\n                ></cv-dropdown>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Comments</label>\r\n                <cv-text-area\r\n                  v-model=\"bulkValidationComments\"\r\n                  label=\"Comments\"\r\n                  placeholder=\"Enter validation comments\"\r\n                ></cv-text-area>\r\n              </div>\r\n\r\n              <div class=\"form-actions\">\r\n                <cv-button\r\n                  kind=\"primary\"\r\n                  @click=\"validateAllItems\"\r\n                >\r\n                  Validate All Unvalidated Items\r\n                </cv-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Modal Actions -->\r\n          <div class=\"modal-actions\">\r\n            <cv-button kind=\"secondary\" @click=\"detailsModalVisible = false\">Close</cv-button>\r\n            <cv-button kind=\"primary\" @click=\"refreshItemDetails\">Refresh</cv-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </cv-modal>\r\n  </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport GaugeChart from '../../components/GaugeChart';\r\nimport MainHeader from '@/components/MainHeader'; // Import the MainHeader component\r\n\r\nexport default {\r\n  name: 'DefectValidations',\r\n  components: {\r\n    GaugeChart,\r\n    MainHeader\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      // PQE Owner data\r\n      selectedPQEOwner: 'All',\r\n      pqeOwners: [],\r\n      pqeOwnerOptions: [{ label: 'All PQE Owners', value: 'All' }],\r\n\r\n      // PQE Validation data\r\n      pqeValidationData: {\r\n        total: 0,\r\n        validated: 0,\r\n        unvalidated: 0,\r\n        percentage: 0\r\n      },\r\n      pqePartNumbers: [],\r\n      pqeChartData: [],\r\n\r\n      // Legacy view data\r\n      selectedIndex: 0, // Track the selected index\r\n      stepClicked: false, // Track whether a step is clicked\r\n      clickedStepName: '', // Track the clicked step's name\r\n      selectedRange: '', // Default range of fails\r\n\r\n      numberOfFails: 0, // Default number of fails\r\n      cableStepsStatus: [\r\n        { label: \"SMP9\", pn: \"02EA657\", complete: true },\r\n        { label: \"Signal\", pn: \"03FM185\", complete: false }, //signal\r\n        { label: \"CDFP\", pn: \"02EC799\", complete: true }, //cdfp\r\n      ],\r\n\r\n      powerThermalSteps: [\r\n        { label: \"Fans\", pn: \"02ED368\", complete: true },\r\n        { label: \"PSU\", pn: \"03KP588\", complete: false }, //signal\r\n        { label: \"PDU\", pn: \"03JG497\", complete: true }, //cdfp\r\n      ],\r\n      gaugeActive: false,\r\n      gaugeData: [\r\n        {\r\n          group: 'value',\r\n          value: 0\r\n        }\r\n      ], // Data for the gauge chart (number of validations left)\r\n      unvalidated_fails: [],\r\n      validated_fails: [],\r\n      unvalidated_count: 0,\r\n      validated_count: 0,\r\n      total_fails: 0,\r\n      perc_val: 0,\r\n      selectedPN: \"\",\r\n\r\n      // New UI data\r\n      isLoading: false,\r\n      loadingError: null,\r\n      searchQuery: '',\r\n      selectedProcess: 'All',\r\n      processOptions: ['All', 'FUL', 'FAB', 'Power', 'Cable', 'Memory'],\r\n      selectedTimeRange: 'Last 3 Months',\r\n      rangeOptions: [\"Last Month\", \"Last 3 Months\", \"Last 6 Months\", \"Last Year\", \"All Time\"],\r\n\r\n      // Validation data\r\n      validationColumns: ['Part Number', 'Group', 'Total', 'Validated', 'Unvalidated', 'Validation %', 'Actions'],\r\n      validationDetailsColumns: ['Defect ID', 'Part Number', 'Serial Number', 'Date', 'Status', 'Root Cause', 'Actions'],\r\n      validationsByGroup: [],\r\n      validationsByPart: [],\r\n\r\n      // Modal data\r\n      detailsModalVisible: false,\r\n      selectedItem: null,\r\n      selectedItemDetails: [],\r\n      bulkValidationRootCause: '',\r\n      bulkValidationComments: '',\r\n      rootCauseOptions: ['Design Issue', 'Manufacturing Defect', 'Material Issue', 'Test Error', 'Handling Damage', 'Unknown'],\r\n\r\n      // Summary data\r\n      validationsSummary: {\r\n        total: 0,\r\n        validated: 0,\r\n        unvalidated: 0,\r\n        validatedPercentage: 0,\r\n        unvalidatedPercentage: 0\r\n      },\r\n\r\n      // Date tracking\r\n      currentDate: '',\r\n      selectedMonthDate: '',\r\n      selectedWeekDate: '',\r\n      expandedSideNav: false,\r\n      useFixed: true\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    hasUnvalidatedItems() {\r\n      return this.selectedItemDetails.some(item => item.status !== 'validated');\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    selectedRange(newRange) {\r\n      if (newRange) {\r\n        this.gaugeActive = false;\r\n        this.get_unval();\r\n      }\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.get_dates();\r\n    this.loadPQEOwners();\r\n  },\r\n\r\n  methods: {\r\n    // PQE Owner methods\r\n    async loadPQEOwners() {\r\n      try {\r\n        const config = this.getAuthConfig();\r\n        const response = await fetch('/api-statit2/get_pqe_owners', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...config.headers\r\n          },\r\n          body: JSON.stringify({})\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch PQE owners: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          this.pqeOwners = data.pqe_owners || [];\r\n\r\n          // Update dropdown options\r\n          this.pqeOwnerOptions = [\r\n            { label: 'All PQE Owners', value: 'All' },\r\n            ...this.pqeOwners.map(owner => ({ label: owner, value: owner }))\r\n          ];\r\n\r\n          console.log(`Loaded ${this.pqeOwners.length} PQE owners`);\r\n        } else {\r\n          console.error('Failed to load PQE owners:', data.message);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading PQE owners:', error);\r\n      }\r\n    },\r\n\r\n    // Get authentication config\r\n    getAuthConfig() {\r\n      return {\r\n        headers: {\r\n          'Authorization': 'Bearer ' + (localStorage.getItem('token') || ''),\r\n          'X-User-ID': localStorage.getItem('userId') || ''\r\n        }\r\n      };\r\n    },\r\n\r\n    // Handle PQE owner change\r\n    async handlePQEOwnerChange() {\r\n      console.log(`Selected PQE owner: ${this.selectedPQEOwner}`);\r\n\r\n      if (this.selectedPQEOwner && this.selectedPQEOwner !== 'All') {\r\n        await this.loadPQEValidationData();\r\n      } else {\r\n        // Reset PQE data when \"All\" is selected\r\n        this.pqeValidationData = {\r\n          total: 0,\r\n          validated: 0,\r\n          unvalidated: 0,\r\n          percentage: 0\r\n        };\r\n        this.pqePartNumbers = [];\r\n        this.pqeChartData = [];\r\n      }\r\n\r\n      this.updateValidationsSummary();\r\n    },\r\n\r\n    // Update validations summary\r\n    updateValidationsSummary() {\r\n      // Use PQE validation data if a specific PQE is selected\r\n      if (this.selectedPQEOwner && this.selectedPQEOwner !== 'All') {\r\n        this.validationsSummary = {\r\n          total: this.pqeValidationData.total,\r\n          validated: this.pqeValidationData.validated,\r\n          unvalidated: this.pqeValidationData.unvalidated,\r\n          validatedPercentage: this.pqeValidationData.percentage,\r\n          unvalidatedPercentage: this.pqeValidationData.total > 0 ? 100 - this.pqeValidationData.percentage : 0\r\n        };\r\n      } else {\r\n        // Reset summary when no specific PQE is selected\r\n        this.validationsSummary = {\r\n          total: 0,\r\n          validated: 0,\r\n          unvalidated: 0,\r\n          validatedPercentage: 0,\r\n          unvalidatedPercentage: 0\r\n        };\r\n      }\r\n    },\r\n\r\n    // Load PQE validation data\r\n    async loadPQEValidationData() {\r\n      if (!this.selectedPQEOwner || this.selectedPQEOwner === 'All') {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        console.log(`Loading validation data for PQE owner: ${this.selectedPQEOwner}`);\r\n\r\n        // Get part numbers for this PQE owner\r\n        this.pqePartNumbers = await this.getPQEPartNumbers(this.selectedPQEOwner);\r\n\r\n        if (this.pqePartNumbers.length === 0) {\r\n          console.warn(`No part numbers found for PQE owner: ${this.selectedPQEOwner}`);\r\n          this.pqeValidationData = {\r\n            total: 0,\r\n            validated: 0,\r\n            unvalidated: 0,\r\n            percentage: 0\r\n          };\r\n          this.pqeChartData = [];\r\n          return;\r\n        }\r\n\r\n        // Query validation data for these part numbers\r\n        let totalValidated = 0;\r\n        let totalUnvalidated = 0;\r\n        let totalFails = 0;\r\n\r\n        const user_type = this.$store.getters.getUser_type;\r\n        const token = this.$store.getters.getToken;\r\n        const action = \"view\";\r\n        const startdate = this.getStartDateFromRange(this.selectedTimeRange);\r\n\r\n        // Process each part number to get validation counts\r\n        for (const pn of this.pqePartNumbers) {\r\n          try {\r\n            const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n                Authorization: \"Bearer \" + token,\r\n              },\r\n              body: JSON.stringify({ \"PN\": pn, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n            });\r\n\r\n            if (response.ok) {\r\n              const data = await this.handleResponse(response);\r\n              if (data && data.status_res === \"success\") {\r\n                totalValidated += data.validated_count || 0;\r\n                totalUnvalidated += data.unvalidated_count || 0;\r\n                totalFails += (data.validated_count || 0) + (data.unvalidated_count || 0);\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error fetching validation data for part ${pn}:`, error);\r\n          }\r\n        }\r\n\r\n        // Update PQE validation data\r\n        const percentage = totalFails > 0 ? Math.round((totalValidated / totalFails) * 100) : 0;\r\n\r\n        this.pqeValidationData = {\r\n          total: totalFails,\r\n          validated: totalValidated,\r\n          unvalidated: totalUnvalidated,\r\n          percentage: percentage\r\n        };\r\n\r\n        // Update chart data for gauge chart\r\n        this.pqeChartData = [\r\n          {\r\n            group: 'value',\r\n            value: percentage\r\n          }\r\n        ];\r\n\r\n        console.log(`PQE ${this.selectedPQEOwner} validation data:`, this.pqeValidationData);\r\n\r\n      } catch (error) {\r\n        console.error(`Error loading PQE validation data:`, error);\r\n      }\r\n    },\r\n\r\n    // Get part numbers for a PQE owner\r\n    async getPQEPartNumbers(pqeOwner) {\r\n      try {\r\n        // This method reads Excel files to get part numbers associated with a PQE owner\r\n        // It follows the same logic as the validation2 controller\r\n\r\n        // For now, we'll make an API call to get this data\r\n        // In the future, this could be optimized to read the Excel files directly on the client\r\n        const config = this.getAuthConfig();\r\n        const response = await fetch('/api-statit2/get_pqe_part_numbers', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...config.headers\r\n          },\r\n          body: JSON.stringify({ pqeOwner })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch PQE part numbers: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status === 'success') {\r\n          console.log(`Found ${data.partNumbers.length} part numbers for PQE owner ${pqeOwner}`);\r\n          return data.partNumbers;\r\n        } else {\r\n          console.error('Failed to get PQE part numbers:', data.message);\r\n          return [];\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error getting part numbers for PQE owner ${pqeOwner}:`, error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    // Legacy methods\r\n    async get_unval() {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let action = \"view\";\r\n        let startdate = \"\"\r\n        let token = this.$store.getters.getToken;\r\n        console.log(\"TOKEN\", token)\r\n        if (this.selectedRange === \"Past Month\"){\r\n          startdate = this.selectedMonthDate\r\n        } else if (this.selectedRange === \"Past Week\"){\r\n          startdate = this.selectedWeekDate\r\n        }\r\n\r\n        // Fetch data from the API\r\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: \"Bearer \" + token,\r\n          },\r\n          body: JSON.stringify({ \"PN\": this.selectedPN, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n        });\r\n\r\n        if (response.status === 401) {\r\n          console.error(\"Unauthorized: Check your token or credentials.\");\r\n        }\r\n\r\n        const data = await this.handleResponse(response);\r\n\r\n        if (data.status_res === \"success\") {\r\n          this.unvalidated_fails = data.unvalidated_fails\r\n          this.validated_fails = data.validated_fails\r\n          this.unvalidated_count = data.unvalidated_count\r\n          this.validated_count = data.validated_count\r\n          this.total_fails = data.total_fails\r\n          this.perc_val = data.perc_val\r\n\r\n          console.log(\"Received data:\", data);\r\n          if(this.perc_val === null){\r\n            this.total_fails = \"No entries\"\r\n          }else{\r\n            this.gaugeActive = true;\r\n          }\r\n\r\n          this.gaugeData = [\r\n            {\r\n              group: 'value',\r\n              value: data.perc_val\r\n            }\r\n          ];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading data:\", error);\r\n      }\r\n    },\r\n\r\n    get_dates() {\r\n      const currentDate = new Date();\r\n      const previousMonthDate = new Date();\r\n      const previousWeekDate = new Date();\r\n\r\n      previousMonthDate.setMonth(currentDate.getMonth() - 1);\r\n      // Subtract 7 days from the current date\r\n      previousWeekDate.setDate(currentDate.getDate() - 7);\r\n\r\n      // Format the dates (e.g., YYYY-MM-DD)\r\n      const formatDate = (date) => {\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed\r\n        const day = String(date.getDate()).padStart(2, '0');\r\n        return `${year}-${month}-${day}`;\r\n      };\r\n\r\n      // Create the selectedRange string\r\n      this.selectedWeekDate = formatDate(previousWeekDate)\r\n      this.selectedMonthDate = formatDate(previousMonthDate)\r\n      this.currentDate = formatDate(currentDate)\r\n    },\r\n\r\n    stepClick(pn, label) {\r\n      this.gaugeActive = false;\r\n      this.stepClicked = true; // Show the clicked info section\r\n      this.selectedPN = pn;\r\n      this.get_unval();\r\n      this.clickedStepName = `${pn} - ${label}`; // Update the clicked step's name\r\n    },\r\n\r\n    validateEach() {\r\n      console.log('Validating each for:', this.selectedPN);\r\n      // Implement logic for validating each item here\r\n    },\r\n\r\n    validateBulk() {\r\n      console.log('Validating bulk for:', this.selectedPN);\r\n      // Implement logic for bulk validation here\r\n    },\r\n\r\n    handleResponse(response) {\r\n      if (!response.ok) {\r\n        if (response.status === 401) {\r\n          this.session_expired_visible = true;\r\n        }\r\n      } else {\r\n        return response.json();\r\n      }\r\n    },\r\n\r\n    reset() {\r\n      this.stepClicked = false; // Reset the step clicked status\r\n      this.clickedStepName = 'Choose PN'; // Reset the clicked step name\r\n      this.selectedRange = 'Monthly'; // Reset the selected range\r\n      this.gaugeActive = false; // Hide the gauge chart\r\n      this.selectedPN = ''; // Reset the selected part number\r\n      // Any other reset logic can go here\r\n      console.log('Content switcher reset');\r\n    },\r\n\r\n    // Utility methods\r\n\r\n    async getPartNumbersForProcess(process) {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let token = this.$store.getters.getToken;\r\n\r\n        // If \"All\" is selected, get all Metis part numbers\r\n        if (process === 'All') {\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_part_numbers\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ user_type }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\" && data.pns) {\r\n            console.log(`Retrieved ${data.pns.length} part numbers from Metis file`);\r\n            return data.pns;\r\n          }\r\n        } else {\r\n          // For specific processes, use the commodity filter\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_pns_from_excel\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ commodity: process, user_type }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\" && data.pns) {\r\n            console.log(`Retrieved ${data.pns.length} part numbers for ${process} from commodity file`);\r\n            return data.pns;\r\n          }\r\n        }\r\n\r\n        // Fallback to hardcoded values if API fails\r\n        console.warn(\"Falling back to hardcoded part numbers\");\r\n        const fallbackParts = {\r\n          'All': [\"02EA657\", \"03FM185\", \"02EC799\", \"02ED368\", \"03KP588\", \"03JG497\", \"01KP123\", \"02FM456\", \"03EC789\"],\r\n          'FUL': [\"01KP123\", \"02FM456\", \"03EC789\"],\r\n          'FAB': [\"04KP321\", \"05FM654\", \"06EC987\"],\r\n          'Power': [\"02ED368\", \"03KP588\", \"03JG497\"],\r\n          'Cable': [\"02EA657\", \"03FM185\", \"02EC799\"],\r\n          'Memory': [\"07KP111\", \"08FM222\", \"09EC333\"]\r\n        };\r\n\r\n        return fallbackParts[process] || fallbackParts['All'];\r\n      } catch (error) {\r\n        console.error(\"Error fetching part numbers:\", error);\r\n        // Return fallback values in case of error\r\n        return [\"02EA657\", \"03FM185\", \"02EC799\"];\r\n      }\r\n    },\r\n\r\n    async getMetisGroupForPartNumber(partNumber) {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let token = this.$store.getters.getToken;\r\n\r\n        // First try to get the Metis breakout names\r\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_breakout_names\", {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: \"Bearer \" + token,\r\n          },\r\n          body: JSON.stringify({ user_type }),\r\n        });\r\n\r\n        const data = await this.handleResponse(response);\r\n\r\n        if (data && data.status_res === \"success\" && data.breakoutMap) {\r\n          // The API should return a mapping of part numbers to breakout names\r\n          const breakoutName = data.breakoutMap[partNumber];\r\n          if (breakoutName) {\r\n            return breakoutName;\r\n          }\r\n        }\r\n\r\n        // If we couldn't get the breakout name from the API, use a fallback mapping\r\n        // This is a simplified version of what would be in the Excel file\r\n        const fallbackGroupMap = {\r\n          \"02EA657\": \"SMP9 Cables\",\r\n          \"03FM185\": \"Signal Cables\",\r\n          \"02EC799\": \"CDFP Cables\",\r\n          \"02ED368\": \"Cooling Fans\",\r\n          \"03KP588\": \"Power Supply Units\",\r\n          \"03JG497\": \"Power Distribution\",\r\n          \"01KP123\": \"Memory Module A\",\r\n          \"02FM456\": \"Memory Module B\",\r\n          \"03EC789\": \"Memory Module C\",\r\n          \"04KP321\": \"Processor Module A\",\r\n          \"05FM654\": \"Processor Module B\",\r\n          \"06EC987\": \"Processor Module C\",\r\n          \"07KP111\": \"Storage Module A\",\r\n          \"08FM222\": \"Storage Module B\",\r\n          \"09EC333\": \"Storage Module C\"\r\n        };\r\n\r\n        return fallbackGroupMap[partNumber] || \"Unknown Group\";\r\n      } catch (error) {\r\n        console.error(\"Error fetching Metis group for part number:\", error);\r\n        // Return a generic group name in case of error\r\n        return \"Unknown Group\";\r\n      }\r\n    },\r\n\r\n    getStartDateFromRange(range) {\r\n      const now = new Date();\r\n      let startDate = new Date();\r\n\r\n      switch (range) {\r\n        case \"Last Month\":\r\n          startDate.setMonth(now.getMonth() - 1);\r\n          break;\r\n        case \"Last 3 Months\":\r\n          startDate.setMonth(now.getMonth() - 3);\r\n          break;\r\n        case \"Last 6 Months\":\r\n          startDate.setMonth(now.getMonth() - 6);\r\n          break;\r\n        case \"Last Year\":\r\n          startDate.setFullYear(now.getFullYear() - 1);\r\n          break;\r\n        case \"All Time\":\r\n          startDate = new Date(2000, 0, 1); // Far in the past\r\n          break;\r\n        default:\r\n          startDate.setMonth(now.getMonth() - 3); // Default to 3 months\r\n      }\r\n\r\n      // Format date as YYYY-MM-DD\r\n      return startDate.toISOString().split('T')[0];\r\n    },\r\n\r\n    filterValidations() {\r\n      // Filtering is handled by computed properties\r\n      console.log(\"Filtering with query:\", this.searchQuery);\r\n    },\r\n\r\n\r\n\r\n    async loadItemDetails(item) {\r\n      this.selectedItemDetails = [];\r\n\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let action = \"view\";\r\n        let startdate = this.getStartDateFromRange(this.selectedTimeRange);\r\n        let token = this.$store.getters.getToken;\r\n\r\n        console.log(`Loading details for ${item.partNumber ? 'part ' + item.partNumber : 'group ' + item.name}`);\r\n\r\n        // If it's a part number item\r\n        if (item.partNumber) {\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ \"PN\": item.partNumber, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\") {\r\n            // Combine validated and unvalidated fails\r\n            const validatedItems = (data.validated_fails || []).map(item => ({\r\n              ...item,\r\n              status: 'validated'\r\n            }));\r\n\r\n            const unvalidatedItems = (data.unvalidated_fails || []).map(item => ({\r\n              ...item,\r\n              status: 'unvalidated'\r\n            }));\r\n\r\n            this.selectedItemDetails = [...validatedItems, ...unvalidatedItems];\r\n            console.log(`Loaded ${this.selectedItemDetails.length} validation items for part ${item.partNumber}`);\r\n          }\r\n        }\r\n        // If it's a group item (Metis grouping)\r\n        else if (item.name) {\r\n          // First try to get part numbers for this Metis group from the API\r\n          let partNumbers = [];\r\n\r\n          try {\r\n            // Try to get part numbers for this Metis group from the API\r\n            const metisResponse = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_part_numbers\", {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n                Authorization: \"Bearer \" + token,\r\n              },\r\n              body: JSON.stringify({ breakoutName: item.name, user_type }),\r\n            });\r\n\r\n            const metisData = await this.handleResponse(metisResponse);\r\n\r\n            if (metisData && metisData.status_res === \"success\" && metisData.pns && metisData.pns.length > 0) {\r\n              partNumbers = metisData.pns;\r\n              console.log(`Retrieved ${partNumbers.length} part numbers for Metis group ${item.name} from API`);\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error fetching part numbers for Metis group ${item.name}:`, error);\r\n          }\r\n\r\n          // If we couldn't get part numbers from the API, fall back to the local data\r\n          if (partNumbers.length === 0) {\r\n            partNumbers = this.validationsByPart\r\n              .filter(part => part.group === item.name)\r\n              .map(part => part.partNumber);\r\n\r\n            console.log(`Using ${partNumbers.length} part numbers for Metis group ${item.name} from local data`);\r\n          }\r\n\r\n          if (partNumbers.length === 0) {\r\n            console.warn(`No part numbers found for Metis group ${item.name}`);\r\n            return;\r\n          }\r\n\r\n          // Fetch details for each part number\r\n          let allDetails = [];\r\n\r\n          for (const pn of partNumbers) {\r\n            try {\r\n              const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n                method: \"POST\",\r\n                headers: {\r\n                  \"Content-Type\": \"application/json\",\r\n                  Authorization: \"Bearer \" + token,\r\n                },\r\n                body: JSON.stringify({ \"PN\": pn, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n              });\r\n\r\n              const data = await this.handleResponse(response);\r\n\r\n              if (data && data.status_res === \"success\") {\r\n                // Combine validated and unvalidated fails\r\n                const validatedItems = (data.validated_fails || []).map(item => ({\r\n                  ...item,\r\n                  status: 'validated'\r\n                }));\r\n\r\n                const unvalidatedItems = (data.unvalidated_fails || []).map(item => ({\r\n                  ...item,\r\n                  status: 'unvalidated'\r\n                }));\r\n\r\n                allDetails = [...allDetails, ...validatedItems, ...unvalidatedItems];\r\n                console.log(`Added ${validatedItems.length + unvalidatedItems.length} items for part ${pn}`);\r\n              }\r\n            } catch (error) {\r\n              console.error(`Error fetching validation data for part ${pn}:`, error);\r\n              // Continue with other part numbers\r\n            }\r\n          }\r\n\r\n          this.selectedItemDetails = allDetails;\r\n          console.log(`Loaded ${this.selectedItemDetails.length} total validation items for group ${item.name}`);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading item details:\", error);\r\n      }\r\n    },\r\n\r\n    refreshItemDetails() {\r\n      if (this.selectedItem) {\r\n        this.loadItemDetails(this.selectedItem);\r\n      }\r\n    },\r\n\r\n    validateItem(item) {\r\n      console.log(\"Validating item:\", item);\r\n      // This would typically call an API to validate the item\r\n      // For demo purposes, we'll just update the local state\r\n      item.status = 'validated';\r\n      item.root_cause_1 = this.bulkValidationRootCause || 'Manual Validation';\r\n\r\n      // Update counts\r\n      if (this.selectedItem) {\r\n        this.selectedItem.validated++;\r\n        this.selectedItem.unvalidated--;\r\n        this.selectedItem.percentage = Math.round((this.selectedItem.validated / this.selectedItem.total) * 100);\r\n      }\r\n    },\r\n\r\n    validateAllItems() {\r\n      if (!this.bulkValidationRootCause) {\r\n        alert(\"Please select a root cause before validating all items.\");\r\n        return;\r\n      }\r\n\r\n      // Find all unvalidated items\r\n      const unvalidatedItems = this.selectedItemDetails.filter(item => item.status !== 'validated');\r\n\r\n      // Validate each item\r\n      unvalidatedItems.forEach(item => {\r\n        item.status = 'validated';\r\n        item.root_cause_1 = this.bulkValidationRootCause;\r\n      });\r\n\r\n      // Update counts\r\n      if (this.selectedItem) {\r\n        this.selectedItem.validated = this.selectedItem.total;\r\n        this.selectedItem.unvalidated = 0;\r\n        this.selectedItem.percentage = 100;\r\n      }\r\n\r\n      // Clear form\r\n      this.bulkValidationRootCause = '';\r\n      this.bulkValidationComments = '';\r\n    },\r\n\r\n    getValidationTagKind(percentage) {\r\n      if (percentage >= 90) return 'green';\r\n      if (percentage >= 70) return 'teal';\r\n      if (percentage >= 50) return 'blue';\r\n      if (percentage >= 30) return 'purple';\r\n      if (percentage >= 10) return 'magenta';\r\n      return 'red';\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return 'N/A';\r\n\r\n      // If it's already in a readable format, return as is\r\n      if (typeof dateString === 'string' && dateString.includes('/')) {\r\n        return dateString;\r\n      }\r\n\r\n      // Try to parse the date\r\n      try {\r\n        const date = new Date(dateString);\r\n        return date.toLocaleDateString();\r\n      } catch (e) {\r\n        return dateString;\r\n      }\r\n    },\r\n\r\n    viewData() {\r\n      console.log(\"View data clicked\");\r\n      // Implement view data functionality\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import \"../../styles/carbon-utils\";\r\n\r\n/* Main container */\r\n.validations-container {\r\n  min-height: 100vh;\r\n  background-color: #161616;\r\n  color: #f4f4f4;\r\n}\r\n\r\n/* Page header */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n  padding: 2rem 2rem 1rem;\r\n  border-bottom: 1px solid #333333;\r\n}\r\n\r\n.page-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.75rem;\r\n  font-weight: 400;\r\n  margin: 0;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 1rem;\r\n}\r\n\r\n/* Filter bar */\r\n.filter-bar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin: 0 2rem 1.5rem;\r\n  background-color: #262626;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.filter-label {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.search-box {\r\n  flex-grow: 1;\r\n  max-width: 300px;\r\n}\r\n\r\n/* Loading and error states */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 3rem;\r\n  margin: 2rem;\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.loading-text {\r\n  margin-top: 1rem;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.error-container {\r\n  margin: 2rem;\r\n}\r\n\r\n/* Content area */\r\n.validations-content {\r\n  padding: 0 2rem 2rem;\r\n}\r\n\r\n.main-validation-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2rem;\r\n}\r\n\r\n.validation-section {\r\n  background-color: #262626;\r\n  border: 1px solid #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n\r\n  h3 {\r\n    color: #f4f4f4;\r\n    font-size: 1.25rem;\r\n    font-weight: 600;\r\n    margin: 0 0 1.5rem 0;\r\n    padding-bottom: 0.75rem;\r\n    border-bottom: 1px solid #393939;\r\n  }\r\n}\r\n\r\n/* Chart container styles */\r\n.chart-container {\r\n  display: flex;\r\n  gap: 2rem;\r\n  margin-bottom: 2rem;\r\n  align-items: center;\r\n}\r\n\r\n.chart-wrapper {\r\n  flex: 0 0 300px;\r\n  height: 300px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.validation-stats {\r\n  flex: 1;\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 1rem;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 1rem;\r\n  background-color: #393939;\r\n  border-radius: 8px;\r\n  border: 1px solid #525252;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 0.875rem;\r\n  color: #c6c6c6;\r\n  margin-bottom: 0.5rem;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.16px;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: #f4f4f4;\r\n\r\n  &.validated {\r\n    color: #42be65;\r\n  }\r\n\r\n  &.unvalidated {\r\n    color: #fa4d56;\r\n  }\r\n}\r\n\r\n.no-data-message {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  color: #8d8d8d;\r\n  font-style: italic;\r\n}\r\n\r\n/* Part numbers summary */\r\n.part-numbers-summary {\r\n  margin-top: 2rem;\r\n\r\n  h4 {\r\n    color: #f4f4f4;\r\n    font-size: 1rem;\r\n    font-weight: 600;\r\n    margin-bottom: 1rem;\r\n  }\r\n}\r\n\r\n.part-numbers-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.part-number-tag {\r\n  font-size: 0.75rem;\r\n}\r\n\r\n/* Empty state */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 3rem;\r\n  color: #8d8d8d;\r\n\r\n  p {\r\n    font-size: 1.125rem;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n/* Summary tiles */\r\n.validations-summary {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 1rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.summary-tile {\r\n  background-color: #262626;\r\n  border: 1px solid #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  text-align: center;\r\n}\r\n\r\n.tile-title {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  margin-bottom: 0.5rem;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.16px;\r\n}\r\n\r\n.tile-value {\r\n  color: #f4f4f4;\r\n  font-size: 2rem;\r\n  font-weight: 600;\r\n  margin-bottom: 0.25rem;\r\n\r\n  &.validated {\r\n    color: #42be65;\r\n  }\r\n\r\n  &.unvalidated {\r\n    color: #fa4d56;\r\n  }\r\n}\r\n\r\n.tile-percentage {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Empty states */\r\n.empty-message {\r\n  color: #8d8d8d;\r\n  text-align: center;\r\n  padding: 2rem;\r\n}\r\n\r\n/* Modal styles */\r\n.validation-details-modal {\r\n  max-width: 1000px;\r\n}\r\n\r\n.modal-content {\r\n  padding: 1.5rem 0;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.modal-section {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.section-title {\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.125rem;\r\n  color: #f4f4f4;\r\n  border-bottom: 1px solid #333333;\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.info-label {\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.375rem;\r\n}\r\n\r\n.info-value {\r\n  font-size: 1rem;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.status-banner {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  margin-bottom: 1.5rem;\r\n  background-color: rgba(15, 98, 254, 0.1);\r\n  border: 1px solid rgba(15, 98, 254, 0.3);\r\n}\r\n\r\n.validation-summary {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.validation-label {\r\n  color: #f4f4f4;\r\n  font-weight: 500;\r\n}\r\n\r\n.bulk-validation-form {\r\n  background-color: #262626;\r\n  padding: 1.5rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.form-label {\r\n  display: block;\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.form-actions {\r\n  margin-top: 1.5rem;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.modal-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 1rem;\r\n  margin-top: 2rem;\r\n}\r\n\r\n/* Legacy view styles */\r\n.legacy-view {\r\n  padding: 1rem;\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.chart-page__grid {\r\n  margin-top: $spacing-08;\r\n}\r\n\r\n.progress-section {\r\n  margin-top: 2rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.button-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  gap: 1rem; /* Adjust space between buttons */\r\n}\r\n\r\n.fail-container {\r\n  display: flex;                /* Enable flexbox */\r\n  flex-direction: column;       /* Stack items vertically */\r\n  align-items: center;          /* Center horizontally */\r\n  justify-content: center;      /* Center vertically */\r\n  text-align: center;           /* Center text */\r\n  margin: 20px;                 /* Optional: Add margin for spacing */\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 1024px) {\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .filter-bar {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .filter-group {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .search-box {\r\n    max-width: 100%;\r\n  }\r\n\r\n  .validations-summary {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .header-actions {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>"]}]}