{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\DefectValidations\\DefectValidations.vue", "mtime": 1748970729948}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DefectValidations.vue"], "names": [], "mappings": ";AAgYA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "DefectValidations.vue", "sourceRoot": "src/views/DefectValidations", "sourcesContent": ["<template>\r\n  <div class=\"validations-container\">\r\n    <!-- Inherit the MainHeader component -->\r\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" />\r\n\r\n    <!-- Page Header -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">Defect Validations</h1>\r\n      <div class=\"header-actions\">\r\n        <cv-button kind=\"primary\" @click=\"refreshData\">Refresh Data</cv-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Filter Bar -->\r\n    <div class=\"filter-bar\">\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">PQE Owner:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedPQEOwner\"\r\n          label=\"Filter by PQE Owner\"\r\n          :items=\"pqeOwnerOptions\"\r\n          @change=\"handlePQEOwnerChange\"\r\n        ></cv-dropdown>\r\n      </div>\r\n\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">Process/Commodity:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedProcess\"\r\n          label=\"Filter by process\"\r\n          :items=\"processOptions\"\r\n          @change=\"loadValidationData\"\r\n        ></cv-dropdown>\r\n      </div>\r\n\r\n      <div class=\"filter-group\">\r\n        <div class=\"filter-label\">Time Period:</div>\r\n        <cv-dropdown\r\n          v-model=\"selectedTimeRange\"\r\n          label=\"Filter by time period\"\r\n          :items=\"rangeOptions\"\r\n          @change=\"loadValidationData\"\r\n        ></cv-dropdown>\r\n      </div>\r\n\r\n      <div class=\"search-box\">\r\n        <cv-search\r\n          v-model=\"searchQuery\"\r\n          label=\"Search\"\r\n          placeholder=\"Search by part number or group...\"\r\n          @input=\"filterValidations\"\r\n        ></cv-search>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div v-if=\"isLoading\" class=\"loading-container\">\r\n      <cv-loading :active=\"true\" :small=\"false\" :withOverlay=\"false\" />\r\n      <p class=\"loading-text\">Loading validation data...</p>\r\n    </div>\r\n\r\n    <!-- Error State -->\r\n    <div v-else-if=\"loadingError\" class=\"error-container\">\r\n      <cv-inline-notification\r\n        kind=\"error\"\r\n        :title=\"'Error'\"\r\n        :sub-title=\"loadingError\"\r\n      />\r\n    </div>\r\n\r\n    <!-- Content when data is loaded -->\r\n    <div v-else class=\"validations-content\">\r\n      <!-- Summary Tiles -->\r\n      <div class=\"validations-summary\">\r\n        <cv-tile class=\"summary-tile\">\r\n          <h4 class=\"tile-title\">Total Validations</h4>\r\n          <p class=\"tile-value\">{{ validationsSummary.total }}</p>\r\n        </cv-tile>\r\n\r\n        <cv-tile class=\"summary-tile\">\r\n          <h4 class=\"tile-title\">Validated</h4>\r\n          <p class=\"tile-value validated\">{{ validationsSummary.validated }}</p>\r\n          <p class=\"tile-percentage\">{{ validationsSummary.validatedPercentage }}%</p>\r\n        </cv-tile>\r\n\r\n        <cv-tile class=\"summary-tile\">\r\n          <h4 class=\"tile-title\">Unvalidated</h4>\r\n          <p class=\"tile-value unvalidated\">{{ validationsSummary.unvalidated }}</p>\r\n          <p class=\"tile-percentage\">{{ validationsSummary.unvalidatedPercentage }}%</p>\r\n        </cv-tile>\r\n      </div>\r\n\r\n      <!-- Main Content (removed tabs) -->\r\n      <div class=\"main-validation-content\">\r\n        <!-- Metis Groupings Section -->\r\n        <div class=\"validation-section\">\r\n          <h3>Validation by Metis Grouping</h3>\r\n          <cv-structured-list>\r\n            <cv-structured-list-head>\r\n              <cv-structured-list-row>\r\n                <cv-structured-list-header>Metis Grouping</cv-structured-list-header>\r\n                <cv-structured-list-header>Total</cv-structured-list-header>\r\n                <cv-structured-list-header>Validated</cv-structured-list-header>\r\n                <cv-structured-list-header>Unvalidated</cv-structured-list-header>\r\n                <cv-structured-list-header>Validation %</cv-structured-list-header>\r\n                <cv-structured-list-header>Actions</cv-structured-list-header>\r\n              </cv-structured-list-row>\r\n            </cv-structured-list-head>\r\n            <cv-structured-list-body>\r\n              <cv-structured-list-row\r\n                v-for=\"(group, index) in filteredValidationsByGroup\"\r\n                :key=\"index\"\r\n              >\r\n                <cv-structured-list-cell>{{ group.name }}</cv-structured-list-cell>\r\n                <cv-structured-list-cell>{{ group.total }}</cv-structured-list-cell>\r\n                <cv-structured-list-cell>{{ group.validated }}</cv-structured-list-cell>\r\n                <cv-structured-list-cell>{{ group.unvalidated }}</cv-structured-list-cell>\r\n                <cv-structured-list-cell>\r\n                  <cv-tag :kind=\"getValidationTagKind(group.percentage)\">\r\n                    {{ group.percentage }}%\r\n                  </cv-tag>\r\n                </cv-structured-list-cell>\r\n                <cv-structured-list-cell>\r\n                  <cv-button\r\n                    kind=\"ghost\"\r\n                    size=\"small\"\r\n                    @click=\"viewGroupDetails(group)\"\r\n                  >\r\n                    View Details\r\n                  </cv-button>\r\n                </cv-structured-list-cell>\r\n              </cv-structured-list-row>\r\n\r\n              <!-- Empty state -->\r\n              <cv-structured-list-row v-if=\"filteredValidationsByGroup.length === 0\">\r\n                <cv-structured-list-cell colspan=\"6\" class=\"empty-message\">\r\n                  No validation data found for the selected filters.\r\n                </cv-structured-list-cell>\r\n              </cv-structured-list-row>\r\n            </cv-structured-list-body>\r\n          </cv-structured-list>\r\n        </div>\r\n\r\n        <!-- Part Numbers Section -->\r\n        <div class=\"validation-section\">\r\n          <h3>Validation by Part Number</h3>\r\n          <cv-data-table\r\n            :columns=\"validationColumns\"\r\n            :pagination=\"{ pageSize: 10 }\"\r\n            :title=\"''\"\r\n          >\r\n            <template slot=\"data\">\r\n              <cv-data-table-row\r\n                v-for=\"(part, index) in filteredValidationsByPart\"\r\n                :key=\"index\"\r\n              >\r\n                <cv-data-table-cell>{{ part.partNumber }}</cv-data-table-cell>\r\n                <cv-data-table-cell>{{ part.group }}</cv-data-table-cell>\r\n                <cv-data-table-cell>{{ part.total }}</cv-data-table-cell>\r\n                <cv-data-table-cell>{{ part.validated }}</cv-data-table-cell>\r\n                <cv-data-table-cell>{{ part.unvalidated }}</cv-data-table-cell>\r\n                <cv-data-table-cell>\r\n                  <cv-tag :kind=\"getValidationTagKind(part.percentage)\">\r\n                    {{ part.percentage }}%\r\n                  </cv-tag>\r\n                </cv-data-table-cell>\r\n                <cv-data-table-cell>\r\n                  <cv-button\r\n                    kind=\"ghost\"\r\n                    size=\"small\"\r\n                    @click=\"viewPartDetails(part)\"\r\n                  >\r\n                    View Details\r\n                  </cv-button>\r\n                </cv-data-table-cell>\r\n              </cv-data-table-row>\r\n\r\n              <!-- Empty state -->\r\n              <cv-data-table-row v-if=\"filteredValidationsByPart.length === 0\">\r\n                <cv-data-table-cell colspan=\"7\" class=\"empty-message\">\r\n                  No validation data found for the selected filters.\r\n                </cv-data-table-cell>\r\n              </cv-data-table-row>\r\n            </template>\r\n          </cv-data-table>\r\n        </div>\r\n\r\n        <!-- Legacy View Section -->\r\n        <div class=\"validation-section legacy-view\">\r\n          <h3>Legacy Validation View</h3>\r\n          <cv-content-switcher aria-label=\"Choose content\" @selected='reset()'>\r\n            <cv-content-switcher-button content-selector=\".content-1\" :selected=\"selectedIndex === 0\">Cables</cv-content-switcher-button>\r\n            <cv-content-switcher-button content-selector=\".content-2\" :selected=\"selectedIndex === 1\">Power/Thermal</cv-content-switcher-button>\r\n          </cv-content-switcher>\r\n\r\n          <div class=\"progress-section content-1\">\r\n            <cv-progress>\r\n              <cv-progress-step\r\n                v-for=\"(step, index) in cableStepsStatus\"\r\n                :key=\"index\"\r\n                :label=\"step.label\"\r\n                :complete=\"step.complete\"\r\n                @step-clicked=\"stepClick(step.pn, step.label)\"\r\n              ></cv-progress-step>\r\n            </cv-progress>\r\n          </div>\r\n\r\n          <div class=\"progress-section content-2\">\r\n            <cv-progress>\r\n              <cv-progress-step\r\n                v-for=\"(step, index) in powerThermalSteps\"\r\n                :key=\"index\"\r\n                :label=\"step.label\"\r\n                :complete=\"step.complete\"\r\n                @step-clicked=\"stepClick(step.pn, step.label)\"\r\n              ></cv-progress-step>\r\n            </cv-progress>\r\n          </div>\r\n\r\n          <h3>{{ clickedStepName }}</h3>\r\n\r\n          <div v-if=\"stepClicked\">\r\n            <cv-dropdown\r\n              label=\"Range of Fails\"\r\n              v-model=\"selectedRange\"\r\n              :items=\"rangeOptions\"\r\n              :selected-item=\"selectedRange\"\r\n            ></cv-dropdown>\r\n\r\n            <GaugeChart v-if=\"gaugeActive\" :data=\"gaugeData\" />\r\n\r\n            <div class=\"fail-container\">\r\n              <p><strong>Unvalidated count:</strong> {{ unvalidated_count }}</p>\r\n              <p><strong>Total number of Fails:</strong> {{ total_fails }}</p>\r\n\r\n              <!-- Buttons under the bar chart -->\r\n              <div class=\"button-container\">\r\n                <cv-button @click=\"viewData\">View Data</cv-button>\r\n                <cv-button @click=\"validateEach\">Validate Each</cv-button>\r\n                <cv-button @click=\"validateBulk\">Validate Bulk</cv-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Validation Details Modal -->\r\n    <cv-modal\r\n      class=\"validation-details-modal\"\r\n      :visible=\"detailsModalVisible\"\r\n      @modal-hidden=\"detailsModalVisible = false\"\r\n      :size=\"'lg'\"\r\n    >\r\n      <template slot=\"title\">\r\n        <div>{{ selectedItem ? (selectedItem.partNumber ? 'Part Number: ' + selectedItem.partNumber : 'Group: ' + selectedItem.name) : 'Validation Details' }}</div>\r\n      </template>\r\n      <template slot=\"content\">\r\n        <div class=\"modal-content\" v-if=\"selectedItem\">\r\n          <!-- Status Banner -->\r\n          <div class=\"status-banner status-banner-in-progress\">\r\n            <div class=\"validation-summary\">\r\n              <span class=\"validation-label\">Validation Rate:</span>\r\n              <cv-tag :kind=\"getValidationTagKind(selectedItem.percentage)\">\r\n                {{ selectedItem.percentage }}%\r\n              </cv-tag>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Validation Details -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Validation Summary</div>\r\n            <div class=\"info-grid\">\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Total Validations</span>\r\n                <span class=\"info-value\">{{ selectedItem.total }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Validated</span>\r\n                <span class=\"info-value\">{{ selectedItem.validated }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Unvalidated</span>\r\n                <span class=\"info-value\">{{ selectedItem.unvalidated }}</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <span class=\"info-label\">Time Period</span>\r\n                <span class=\"info-value\">{{ selectedTimeRange }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Validation Items Table -->\r\n          <div class=\"modal-section\">\r\n            <div class=\"section-title\">Validation Items</div>\r\n            <cv-data-table\r\n              :columns=\"validationDetailsColumns\"\r\n              :pagination=\"{ pageSize: 10 }\"\r\n              :title=\"''\"\r\n            >\r\n              <template slot=\"data\">\r\n                <cv-data-table-row\r\n                  v-for=\"(item, index) in selectedItemDetails\"\r\n                  :key=\"index\"\r\n                >\r\n                  <cv-data-table-cell>{{ item.defect_id }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.pn }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.sn }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ formatDate(item.date) }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>\r\n                    <cv-tag :kind=\"item.status === 'validated' ? 'green' : 'red'\">\r\n                      {{ item.status === 'validated' ? 'Validated' : 'Unvalidated' }}\r\n                    </cv-tag>\r\n                  </cv-data-table-cell>\r\n                  <cv-data-table-cell>{{ item.root_cause_1 || 'N/A' }}</cv-data-table-cell>\r\n                  <cv-data-table-cell>\r\n                    <cv-button\r\n                      kind=\"ghost\"\r\n                      size=\"small\"\r\n                      @click=\"validateItem(item)\"\r\n                      v-if=\"item.status !== 'validated'\"\r\n                    >\r\n                      Validate\r\n                    </cv-button>\r\n                    <span v-else>-</span>\r\n                  </cv-data-table-cell>\r\n                </cv-data-table-row>\r\n\r\n                <!-- Empty state -->\r\n                <cv-data-table-row v-if=\"selectedItemDetails.length === 0\">\r\n                  <cv-data-table-cell colspan=\"7\" class=\"empty-message\">\r\n                    No validation items found.\r\n                  </cv-data-table-cell>\r\n                </cv-data-table-row>\r\n              </template>\r\n            </cv-data-table>\r\n          </div>\r\n\r\n          <!-- Bulk Validation Section -->\r\n          <div class=\"modal-section\" v-if=\"hasUnvalidatedItems\">\r\n            <div class=\"section-title\">Bulk Validation</div>\r\n            <div class=\"bulk-validation-form\">\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Root Cause</label>\r\n                <cv-dropdown\r\n                  v-model=\"bulkValidationRootCause\"\r\n                  label=\"Select Root Cause\"\r\n                  :items=\"rootCauseOptions\"\r\n                ></cv-dropdown>\r\n              </div>\r\n\r\n              <div class=\"form-group\">\r\n                <label class=\"form-label\">Comments</label>\r\n                <cv-text-area\r\n                  v-model=\"bulkValidationComments\"\r\n                  label=\"Comments\"\r\n                  placeholder=\"Enter validation comments\"\r\n                ></cv-text-area>\r\n              </div>\r\n\r\n              <div class=\"form-actions\">\r\n                <cv-button\r\n                  kind=\"primary\"\r\n                  @click=\"validateAllItems\"\r\n                >\r\n                  Validate All Unvalidated Items\r\n                </cv-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Modal Actions -->\r\n          <div class=\"modal-actions\">\r\n            <cv-button kind=\"secondary\" @click=\"detailsModalVisible = false\">Close</cv-button>\r\n            <cv-button kind=\"primary\" @click=\"refreshItemDetails\">Refresh</cv-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </cv-modal>\r\n  </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport GaugeChart from '../../components/GaugeChart';\r\nimport MainHeader from '@/components/MainHeader'; // Import the MainHeader component\r\n\r\nexport default {\r\n  name: 'DefectValidations',\r\n  components: {\r\n    GaugeChart,\r\n    MainHeader\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      // PQE Owner data\r\n      selectedPQEOwner: 'All',\r\n      pqeOwners: [],\r\n      pqeOwnerOptions: [{ label: 'All PQE Owners', value: 'All' }],\r\n\r\n      // Legacy view data\r\n      selectedIndex: 0, // Track the selected index\r\n      stepClicked: false, // Track whether a step is clicked\r\n      clickedStepName: '', // Track the clicked step's name\r\n      selectedRange: '', // Default range of fails\r\n\r\n      numberOfFails: 0, // Default number of fails\r\n      cableStepsStatus: [\r\n        { label: \"SMP9\", pn: \"02EA657\", complete: true },\r\n        { label: \"Signal\", pn: \"03FM185\", complete: false }, //signal\r\n        { label: \"CDFP\", pn: \"02EC799\", complete: true }, //cdfp\r\n      ],\r\n\r\n      powerThermalSteps: [\r\n        { label: \"Fans\", pn: \"02ED368\", complete: true },\r\n        { label: \"PSU\", pn: \"03KP588\", complete: false }, //signal\r\n        { label: \"PDU\", pn: \"03JG497\", complete: true }, //cdfp\r\n      ],\r\n      gaugeActive: false,\r\n      gaugeData: [\r\n        {\r\n          group: 'value',\r\n          value: 0\r\n        }\r\n      ], // Data for the gauge chart (number of validations left)\r\n      unvalidated_fails: [],\r\n      validated_fails: [],\r\n      unvalidated_count: 0,\r\n      validated_count: 0,\r\n      total_fails: 0,\r\n      perc_val: 0,\r\n      selectedPN: \"\",\r\n\r\n      // New UI data\r\n      isLoading: false,\r\n      loadingError: null,\r\n      searchQuery: '',\r\n      selectedProcess: 'All',\r\n      processOptions: ['All', 'FUL', 'FAB', 'Power', 'Cable', 'Memory'],\r\n      selectedTimeRange: 'Last 3 Months',\r\n      rangeOptions: [\"Last Month\", \"Last 3 Months\", \"Last 6 Months\", \"Last Year\", \"All Time\"],\r\n\r\n      // Validation data\r\n      validationColumns: ['Part Number', 'Group', 'Total', 'Validated', 'Unvalidated', 'Validation %', 'Actions'],\r\n      validationDetailsColumns: ['Defect ID', 'Part Number', 'Serial Number', 'Date', 'Status', 'Root Cause', 'Actions'],\r\n      validationsByGroup: [],\r\n      validationsByPart: [],\r\n\r\n      // Modal data\r\n      detailsModalVisible: false,\r\n      selectedItem: null,\r\n      selectedItemDetails: [],\r\n      bulkValidationRootCause: '',\r\n      bulkValidationComments: '',\r\n      rootCauseOptions: ['Design Issue', 'Manufacturing Defect', 'Material Issue', 'Test Error', 'Handling Damage', 'Unknown'],\r\n\r\n      // Summary data\r\n      validationsSummary: {\r\n        total: 0,\r\n        validated: 0,\r\n        unvalidated: 0,\r\n        validatedPercentage: 0,\r\n        unvalidatedPercentage: 0\r\n      },\r\n\r\n      // Date tracking\r\n      currentDate: '',\r\n      selectedMonthDate: '',\r\n      selectedWeekDate: '',\r\n      expandedSideNav: false,\r\n      useFixed: true\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    filteredValidationsByGroup() {\r\n      if (!this.searchQuery) {\r\n        return this.validationsByGroup;\r\n      }\r\n\r\n      const query = this.searchQuery.toLowerCase();\r\n      return this.validationsByGroup.filter(group =>\r\n        group.name.toLowerCase().includes(query)\r\n      );\r\n    },\r\n\r\n    filteredValidationsByPart() {\r\n      if (!this.searchQuery) {\r\n        return this.validationsByPart;\r\n      }\r\n\r\n      const query = this.searchQuery.toLowerCase();\r\n      return this.validationsByPart.filter(part =>\r\n        part.partNumber.toLowerCase().includes(query) ||\r\n        part.group.toLowerCase().includes(query)\r\n      );\r\n    },\r\n\r\n    hasUnvalidatedItems() {\r\n      return this.selectedItemDetails.some(item => item.status !== 'validated');\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    selectedRange(newRange) {\r\n      if (newRange) {\r\n        this.gaugeActive = false;\r\n        this.get_unval();\r\n      }\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.get_dates();\r\n    this.loadPQEOwners();\r\n    this.loadValidationData();\r\n  },\r\n\r\n  methods: {\r\n    // PQE Owner methods\r\n    async loadPQEOwners() {\r\n      try {\r\n        const config = this.getAuthConfig();\r\n        const response = await fetch('/api-statit2/get_pqe_owners', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...config.headers\r\n          },\r\n          body: JSON.stringify({})\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch PQE owners: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status_res === 'success') {\r\n          this.pqeOwners = data.pqe_owners || [];\r\n\r\n          // Update dropdown options\r\n          this.pqeOwnerOptions = [\r\n            { label: 'All PQE Owners', value: 'All' },\r\n            ...this.pqeOwners.map(owner => ({ label: owner, value: owner }))\r\n          ];\r\n\r\n          console.log(`Loaded ${this.pqeOwners.length} PQE owners`);\r\n        } else {\r\n          console.error('Failed to load PQE owners:', data.message);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading PQE owners:', error);\r\n      }\r\n    },\r\n\r\n    // Get authentication config\r\n    getAuthConfig() {\r\n      return {\r\n        headers: {\r\n          'Authorization': 'Bearer ' + (localStorage.getItem('token') || ''),\r\n          'X-User-ID': localStorage.getItem('userId') || ''\r\n        }\r\n      };\r\n    },\r\n\r\n    // Handle PQE owner change\r\n    async handlePQEOwnerChange() {\r\n      console.log(`Selected PQE owner: ${this.selectedPQEOwner}`);\r\n      await this.loadValidationData();\r\n      this.updateValidationsSummary();\r\n    },\r\n\r\n    // Update validations summary\r\n    updateValidationsSummary() {\r\n      const totalValidations = this.validationsByGroup.reduce((sum, group) => sum + group.total, 0);\r\n      const totalValidated = this.validationsByGroup.reduce((sum, group) => sum + group.validated, 0);\r\n      const totalUnvalidated = totalValidations - totalValidated;\r\n\r\n      this.validationsSummary = {\r\n        total: totalValidations,\r\n        validated: totalValidated,\r\n        unvalidated: totalUnvalidated,\r\n        validatedPercentage: totalValidations > 0 ? Math.round((totalValidated / totalValidations) * 100) : 0,\r\n        unvalidatedPercentage: totalValidations > 0 ? Math.round((totalUnvalidated / totalValidations) * 100) : 0\r\n      };\r\n    },\r\n\r\n    // Get part numbers for a PQE owner\r\n    async getPQEPartNumbers(pqeOwner) {\r\n      try {\r\n        // This method reads Excel files to get part numbers associated with a PQE owner\r\n        // It follows the same logic as the validation2 controller\r\n\r\n        // For now, we'll make an API call to get this data\r\n        // In the future, this could be optimized to read the Excel files directly on the client\r\n        const config = this.getAuthConfig();\r\n        const response = await fetch('/api-statit2/get_pqe_part_numbers', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            ...config.headers\r\n          },\r\n          body: JSON.stringify({ pqeOwner })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch PQE part numbers: ${response.status} ${response.statusText}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        if (data.status === 'success') {\r\n          console.log(`Found ${data.partNumbers.length} part numbers for PQE owner ${pqeOwner}`);\r\n          return data.partNumbers;\r\n        } else {\r\n          console.error('Failed to get PQE part numbers:', data.message);\r\n          return [];\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error getting part numbers for PQE owner ${pqeOwner}:`, error);\r\n        return [];\r\n      }\r\n    },\r\n\r\n    // Legacy methods\r\n    async get_unval() {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let action = \"view\";\r\n        let startdate = \"\"\r\n        let token = this.$store.getters.getToken;\r\n        console.log(\"TOKEN\", token)\r\n        if (this.selectedRange === \"Past Month\"){\r\n          startdate = this.selectedMonthDate\r\n        } else if (this.selectedRange === \"Past Week\"){\r\n          startdate = this.selectedWeekDate\r\n        }\r\n\r\n        // Fetch data from the API\r\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: \"Bearer \" + token,\r\n          },\r\n          body: JSON.stringify({ \"PN\": this.selectedPN, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n        });\r\n\r\n        if (response.status === 401) {\r\n          console.error(\"Unauthorized: Check your token or credentials.\");\r\n        }\r\n\r\n        const data = await this.handleResponse(response);\r\n\r\n        if (data.status_res === \"success\") {\r\n          this.unvalidated_fails = data.unvalidated_fails\r\n          this.validated_fails = data.validated_fails\r\n          this.unvalidated_count = data.unvalidated_count\r\n          this.validated_count = data.validated_count\r\n          this.total_fails = data.total_fails\r\n          this.perc_val = data.perc_val\r\n\r\n          console.log(\"Received data:\", data);\r\n          if(this.perc_val === null){\r\n            this.total_fails = \"No entries\"\r\n          }else{\r\n            this.gaugeActive = true;\r\n          }\r\n\r\n          this.gaugeData = [\r\n            {\r\n              group: 'value',\r\n              value: data.perc_val\r\n            }\r\n          ];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading data:\", error);\r\n      }\r\n    },\r\n\r\n    get_dates() {\r\n      const currentDate = new Date();\r\n      const previousMonthDate = new Date();\r\n      const previousWeekDate = new Date();\r\n\r\n      previousMonthDate.setMonth(currentDate.getMonth() - 1);\r\n      // Subtract 7 days from the current date\r\n      previousWeekDate.setDate(currentDate.getDate() - 7);\r\n\r\n      // Format the dates (e.g., YYYY-MM-DD)\r\n      const formatDate = (date) => {\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed\r\n        const day = String(date.getDate()).padStart(2, '0');\r\n        return `${year}-${month}-${day}`;\r\n      };\r\n\r\n      // Create the selectedRange string\r\n      this.selectedWeekDate = formatDate(previousWeekDate)\r\n      this.selectedMonthDate = formatDate(previousMonthDate)\r\n      this.currentDate = formatDate(currentDate)\r\n    },\r\n\r\n    stepClick(pn, label) {\r\n      this.gaugeActive = false;\r\n      this.stepClicked = true; // Show the clicked info section\r\n      this.selectedPN = pn;\r\n      this.get_unval();\r\n      this.clickedStepName = `${pn} - ${label}`; // Update the clicked step's name\r\n    },\r\n\r\n    validateEach() {\r\n      console.log('Validating each for:', this.selectedPN);\r\n      // Implement logic for validating each item here\r\n    },\r\n\r\n    validateBulk() {\r\n      console.log('Validating bulk for:', this.selectedPN);\r\n      // Implement logic for bulk validation here\r\n    },\r\n\r\n    handleResponse(response) {\r\n      if (!response.ok) {\r\n        if (response.status === 401) {\r\n          this.session_expired_visible = true;\r\n        }\r\n      } else {\r\n        return response.json();\r\n      }\r\n    },\r\n\r\n    reset() {\r\n      this.stepClicked = false; // Reset the step clicked status\r\n      this.clickedStepName = 'Choose PN'; // Reset the clicked step name\r\n      this.selectedRange = 'Monthly'; // Reset the selected range\r\n      this.gaugeActive = false; // Hide the gauge chart\r\n      this.selectedPN = ''; // Reset the selected part number\r\n      // Any other reset logic can go here\r\n      console.log('Content switcher reset');\r\n    },\r\n\r\n    // New UI methods\r\n    async loadValidationData() {\r\n      this.isLoading = true;\r\n      this.loadingError = null;\r\n\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let action = \"view\";\r\n        let startdate = this.getStartDateFromRange(this.selectedTimeRange);\r\n        let token = this.$store.getters.getToken;\r\n\r\n        console.log(`Loading validation data for process: ${this.selectedProcess}, time range: ${this.selectedTimeRange}, PQE owner: ${this.selectedPQEOwner}`);\r\n\r\n        // Get all part numbers for the selected process\r\n        let partNumbers = await this.getPartNumbersForProcess(this.selectedProcess);\r\n\r\n        if (!partNumbers || partNumbers.length === 0) {\r\n          this.loadingError = \"No part numbers found for the selected process.\";\r\n          this.updateValidationsSummary();\r\n          this.isLoading = false;\r\n          return;\r\n        }\r\n\r\n        // Apply PQE owner filter if selected\r\n        if (this.selectedPQEOwner && this.selectedPQEOwner !== 'All') {\r\n          try {\r\n            const pqePartNumbers = await this.getPQEPartNumbers(this.selectedPQEOwner);\r\n            if (pqePartNumbers.length > 0) {\r\n              // Filter part numbers to only include those associated with the selected PQE owner\r\n              partNumbers = partNumbers.filter(pn => pqePartNumbers.includes(pn.toString()));\r\n              console.log(`Filtered to ${partNumbers.length} part numbers for PQE owner ${this.selectedPQEOwner}`);\r\n            } else {\r\n              console.warn(`No part numbers found for PQE owner: ${this.selectedPQEOwner}`);\r\n              this.validationsByGroup = [];\r\n              this.validationsByPart = [];\r\n              this.updateValidationsSummary();\r\n              this.isLoading = false;\r\n              return;\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error filtering by PQE owner: ${error.message}`);\r\n            // Continue without PQE filter if there's an error\r\n          }\r\n        }\r\n\r\n        console.log(`Processing ${partNumbers.length} part numbers for validation data`);\r\n\r\n        // Initialize validation data arrays\r\n        this.validationsByGroup = [];\r\n        this.validationsByPart = [];\r\n\r\n        // Track totals for summary\r\n        let totalValidations = 0;\r\n        let totalValidated = 0;\r\n        let totalUnvalidated = 0;\r\n\r\n        // Group tracking for aggregation\r\n        const groupMap = new Map();\r\n\r\n        // Get Metis breakout names for all part numbers in one call if possible\r\n        let metisGroups = {};\r\n        try {\r\n          const breakoutResponse = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_breakout_names\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ user_type }),\r\n          });\r\n\r\n          const breakoutData = await this.handleResponse(breakoutResponse);\r\n\r\n          if (breakoutData && breakoutData.status_res === \"success\" && breakoutData.breakoutMap) {\r\n            metisGroups = breakoutData.breakoutMap;\r\n            console.log(`Retrieved Metis breakout names for ${Object.keys(metisGroups).length} part numbers`);\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching Metis breakout names:\", error);\r\n        }\r\n\r\n        // Process each part number\r\n        for (const pn of partNumbers) {\r\n          // Skip empty part numbers\r\n          if (!pn) continue;\r\n\r\n          try {\r\n            // Fetch validation data for this part number\r\n            const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n                Authorization: \"Bearer \" + token,\r\n              },\r\n              body: JSON.stringify({ \"PN\": pn, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n            });\r\n\r\n            if (response.status === 401) {\r\n              console.error(\"Unauthorized: Check your token or credentials.\");\r\n              this.loadingError = \"Authentication error. Please log in again.\";\r\n              this.isLoading = false;\r\n              return;\r\n            }\r\n\r\n            const data = await this.handleResponse(response);\r\n\r\n            if (data && data.status_res === \"success\") {\r\n              // Get the Metis group for this part number\r\n              // First try from the pre-fetched map, then fall back to individual lookup\r\n              let group = metisGroups[pn];\r\n              if (!group) {\r\n                group = await this.getMetisGroupForPartNumber(pn);\r\n              }\r\n\r\n              // Calculate validation stats\r\n              const validated = data.validated_count || 0;\r\n              const unvalidated = data.unvalidated_count || 0;\r\n              const total = validated + unvalidated;\r\n              const percentage = total > 0 ? Math.round((validated / total) * 100) : 0;\r\n\r\n              console.log(`Part ${pn} (${group}): ${validated} validated, ${unvalidated} unvalidated`);\r\n\r\n              // Add to part-level data\r\n              if (total > 0) {\r\n                this.validationsByPart.push({\r\n                  partNumber: pn,\r\n                  group: group,\r\n                  total: total,\r\n                  validated: validated,\r\n                  unvalidated: unvalidated,\r\n                  percentage: percentage\r\n                });\r\n\r\n                // Aggregate to group level\r\n                if (!groupMap.has(group)) {\r\n                  groupMap.set(group, { total: 0, validated: 0, unvalidated: 0 });\r\n                }\r\n\r\n                const groupData = groupMap.get(group);\r\n                groupData.total += total;\r\n                groupData.validated += validated;\r\n                groupData.unvalidated += unvalidated;\r\n\r\n                // Add to summary totals\r\n                totalValidations += total;\r\n                totalValidated += validated;\r\n                totalUnvalidated += unvalidated;\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error processing part number ${pn}:`, error);\r\n            // Continue with other part numbers\r\n          }\r\n        }\r\n\r\n        // Convert group map to array\r\n        for (const [name, data] of groupMap.entries()) {\r\n          const percentage = data.total > 0 ? Math.round((data.validated / data.total) * 100) : 0;\r\n          this.validationsByGroup.push({\r\n            name: name,\r\n            total: data.total,\r\n            validated: data.validated,\r\n            unvalidated: data.unvalidated,\r\n            percentage: percentage\r\n          });\r\n        }\r\n\r\n        // Update summary data\r\n        this.validationsSummary = {\r\n          total: totalValidations,\r\n          validated: totalValidated,\r\n          unvalidated: totalUnvalidated,\r\n          validatedPercentage: totalValidations > 0 ? Math.round((totalValidated / totalValidations) * 100) : 0,\r\n          unvalidatedPercentage: totalValidations > 0 ? Math.round((totalUnvalidated / totalValidations) * 100) : 0\r\n        };\r\n\r\n        // Sort data\r\n        this.validationsByGroup.sort((a, b) => b.unvalidated - a.unvalidated);\r\n        this.validationsByPart.sort((a, b) => b.unvalidated - a.unvalidated);\r\n\r\n        console.log(`Processed validation data: ${this.validationsByGroup.length} groups, ${this.validationsByPart.length} parts`);\r\n\r\n      } catch (error) {\r\n        console.error(\"Error loading validation data:\", error);\r\n        this.loadingError = \"Failed to load validation data. Please try again.\";\r\n      } finally {\r\n        this.isLoading = false;\r\n      }\r\n    },\r\n\r\n    async getPartNumbersForProcess(process) {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let token = this.$store.getters.getToken;\r\n\r\n        // If \"All\" is selected, get all Metis part numbers\r\n        if (process === 'All') {\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_part_numbers\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ user_type }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\" && data.pns) {\r\n            console.log(`Retrieved ${data.pns.length} part numbers from Metis file`);\r\n            return data.pns;\r\n          }\r\n        } else {\r\n          // For specific processes, use the commodity filter\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_pns_from_excel\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ commodity: process, user_type }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\" && data.pns) {\r\n            console.log(`Retrieved ${data.pns.length} part numbers for ${process} from commodity file`);\r\n            return data.pns;\r\n          }\r\n        }\r\n\r\n        // Fallback to hardcoded values if API fails\r\n        console.warn(\"Falling back to hardcoded part numbers\");\r\n        const fallbackParts = {\r\n          'All': [\"02EA657\", \"03FM185\", \"02EC799\", \"02ED368\", \"03KP588\", \"03JG497\", \"01KP123\", \"02FM456\", \"03EC789\"],\r\n          'FUL': [\"01KP123\", \"02FM456\", \"03EC789\"],\r\n          'FAB': [\"04KP321\", \"05FM654\", \"06EC987\"],\r\n          'Power': [\"02ED368\", \"03KP588\", \"03JG497\"],\r\n          'Cable': [\"02EA657\", \"03FM185\", \"02EC799\"],\r\n          'Memory': [\"07KP111\", \"08FM222\", \"09EC333\"]\r\n        };\r\n\r\n        return fallbackParts[process] || fallbackParts['All'];\r\n      } catch (error) {\r\n        console.error(\"Error fetching part numbers:\", error);\r\n        // Return fallback values in case of error\r\n        return [\"02EA657\", \"03FM185\", \"02EC799\"];\r\n      }\r\n    },\r\n\r\n    async getMetisGroupForPartNumber(partNumber) {\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let token = this.$store.getters.getToken;\r\n\r\n        // First try to get the Metis breakout names\r\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_breakout_names\", {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: \"Bearer \" + token,\r\n          },\r\n          body: JSON.stringify({ user_type }),\r\n        });\r\n\r\n        const data = await this.handleResponse(response);\r\n\r\n        if (data && data.status_res === \"success\" && data.breakoutMap) {\r\n          // The API should return a mapping of part numbers to breakout names\r\n          const breakoutName = data.breakoutMap[partNumber];\r\n          if (breakoutName) {\r\n            return breakoutName;\r\n          }\r\n        }\r\n\r\n        // If we couldn't get the breakout name from the API, use a fallback mapping\r\n        // This is a simplified version of what would be in the Excel file\r\n        const fallbackGroupMap = {\r\n          \"02EA657\": \"SMP9 Cables\",\r\n          \"03FM185\": \"Signal Cables\",\r\n          \"02EC799\": \"CDFP Cables\",\r\n          \"02ED368\": \"Cooling Fans\",\r\n          \"03KP588\": \"Power Supply Units\",\r\n          \"03JG497\": \"Power Distribution\",\r\n          \"01KP123\": \"Memory Module A\",\r\n          \"02FM456\": \"Memory Module B\",\r\n          \"03EC789\": \"Memory Module C\",\r\n          \"04KP321\": \"Processor Module A\",\r\n          \"05FM654\": \"Processor Module B\",\r\n          \"06EC987\": \"Processor Module C\",\r\n          \"07KP111\": \"Storage Module A\",\r\n          \"08FM222\": \"Storage Module B\",\r\n          \"09EC333\": \"Storage Module C\"\r\n        };\r\n\r\n        return fallbackGroupMap[partNumber] || \"Unknown Group\";\r\n      } catch (error) {\r\n        console.error(\"Error fetching Metis group for part number:\", error);\r\n        // Return a generic group name in case of error\r\n        return \"Unknown Group\";\r\n      }\r\n    },\r\n\r\n    getStartDateFromRange(range) {\r\n      const now = new Date();\r\n      let startDate = new Date();\r\n\r\n      switch (range) {\r\n        case \"Last Month\":\r\n          startDate.setMonth(now.getMonth() - 1);\r\n          break;\r\n        case \"Last 3 Months\":\r\n          startDate.setMonth(now.getMonth() - 3);\r\n          break;\r\n        case \"Last 6 Months\":\r\n          startDate.setMonth(now.getMonth() - 6);\r\n          break;\r\n        case \"Last Year\":\r\n          startDate.setFullYear(now.getFullYear() - 1);\r\n          break;\r\n        case \"All Time\":\r\n          startDate = new Date(2000, 0, 1); // Far in the past\r\n          break;\r\n        default:\r\n          startDate.setMonth(now.getMonth() - 3); // Default to 3 months\r\n      }\r\n\r\n      // Format date as YYYY-MM-DD\r\n      return startDate.toISOString().split('T')[0];\r\n    },\r\n\r\n    filterValidations() {\r\n      // Filtering is handled by computed properties\r\n      console.log(\"Filtering with query:\", this.searchQuery);\r\n    },\r\n\r\n    refreshData() {\r\n      this.loadValidationData();\r\n    },\r\n\r\n    viewGroupDetails(group) {\r\n      this.selectedItem = group;\r\n      this.loadItemDetails(group);\r\n      this.detailsModalVisible = true;\r\n    },\r\n\r\n    viewPartDetails(part) {\r\n      this.selectedItem = part;\r\n      this.loadItemDetails(part);\r\n      this.detailsModalVisible = true;\r\n    },\r\n\r\n    async loadItemDetails(item) {\r\n      this.selectedItemDetails = [];\r\n\r\n      try {\r\n        let user_type = this.$store.getters.getUser_type;\r\n        let action = \"view\";\r\n        let startdate = this.getStartDateFromRange(this.selectedTimeRange);\r\n        let token = this.$store.getters.getToken;\r\n\r\n        console.log(`Loading details for ${item.partNumber ? 'part ' + item.partNumber : 'group ' + item.name}`);\r\n\r\n        // If it's a part number item\r\n        if (item.partNumber) {\r\n          const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: \"Bearer \" + token,\r\n            },\r\n            body: JSON.stringify({ \"PN\": item.partNumber, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n          });\r\n\r\n          const data = await this.handleResponse(response);\r\n\r\n          if (data && data.status_res === \"success\") {\r\n            // Combine validated and unvalidated fails\r\n            const validatedItems = (data.validated_fails || []).map(item => ({\r\n              ...item,\r\n              status: 'validated'\r\n            }));\r\n\r\n            const unvalidatedItems = (data.unvalidated_fails || []).map(item => ({\r\n              ...item,\r\n              status: 'unvalidated'\r\n            }));\r\n\r\n            this.selectedItemDetails = [...validatedItems, ...unvalidatedItems];\r\n            console.log(`Loaded ${this.selectedItemDetails.length} validation items for part ${item.partNumber}`);\r\n          }\r\n        }\r\n        // If it's a group item (Metis grouping)\r\n        else if (item.name) {\r\n          // First try to get part numbers for this Metis group from the API\r\n          let partNumbers = [];\r\n\r\n          try {\r\n            // Try to get part numbers for this Metis group from the API\r\n            const metisResponse = await fetch(process.env.VUE_APP_API_PATH + \"get_metis_part_numbers\", {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n                Authorization: \"Bearer \" + token,\r\n              },\r\n              body: JSON.stringify({ breakoutName: item.name, user_type }),\r\n            });\r\n\r\n            const metisData = await this.handleResponse(metisResponse);\r\n\r\n            if (metisData && metisData.status_res === \"success\" && metisData.pns && metisData.pns.length > 0) {\r\n              partNumbers = metisData.pns;\r\n              console.log(`Retrieved ${partNumbers.length} part numbers for Metis group ${item.name} from API`);\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error fetching part numbers for Metis group ${item.name}:`, error);\r\n          }\r\n\r\n          // If we couldn't get part numbers from the API, fall back to the local data\r\n          if (partNumbers.length === 0) {\r\n            partNumbers = this.validationsByPart\r\n              .filter(part => part.group === item.name)\r\n              .map(part => part.partNumber);\r\n\r\n            console.log(`Using ${partNumbers.length} part numbers for Metis group ${item.name} from local data`);\r\n          }\r\n\r\n          if (partNumbers.length === 0) {\r\n            console.warn(`No part numbers found for Metis group ${item.name}`);\r\n            return;\r\n          }\r\n\r\n          // Fetch details for each part number\r\n          let allDetails = [];\r\n\r\n          for (const pn of partNumbers) {\r\n            try {\r\n              const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unval\", {\r\n                method: \"POST\",\r\n                headers: {\r\n                  \"Content-Type\": \"application/json\",\r\n                  Authorization: \"Bearer \" + token,\r\n                },\r\n                body: JSON.stringify({ \"PN\": pn, \"SD\": startdate, \"ED\": this.currentDate, user_type, action }),\r\n              });\r\n\r\n              const data = await this.handleResponse(response);\r\n\r\n              if (data && data.status_res === \"success\") {\r\n                // Combine validated and unvalidated fails\r\n                const validatedItems = (data.validated_fails || []).map(item => ({\r\n                  ...item,\r\n                  status: 'validated'\r\n                }));\r\n\r\n                const unvalidatedItems = (data.unvalidated_fails || []).map(item => ({\r\n                  ...item,\r\n                  status: 'unvalidated'\r\n                }));\r\n\r\n                allDetails = [...allDetails, ...validatedItems, ...unvalidatedItems];\r\n                console.log(`Added ${validatedItems.length + unvalidatedItems.length} items for part ${pn}`);\r\n              }\r\n            } catch (error) {\r\n              console.error(`Error fetching validation data for part ${pn}:`, error);\r\n              // Continue with other part numbers\r\n            }\r\n          }\r\n\r\n          this.selectedItemDetails = allDetails;\r\n          console.log(`Loaded ${this.selectedItemDetails.length} total validation items for group ${item.name}`);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading item details:\", error);\r\n      }\r\n    },\r\n\r\n    refreshItemDetails() {\r\n      if (this.selectedItem) {\r\n        this.loadItemDetails(this.selectedItem);\r\n      }\r\n    },\r\n\r\n    validateItem(item) {\r\n      console.log(\"Validating item:\", item);\r\n      // This would typically call an API to validate the item\r\n      // For demo purposes, we'll just update the local state\r\n      item.status = 'validated';\r\n      item.root_cause_1 = this.bulkValidationRootCause || 'Manual Validation';\r\n\r\n      // Update counts\r\n      if (this.selectedItem) {\r\n        this.selectedItem.validated++;\r\n        this.selectedItem.unvalidated--;\r\n        this.selectedItem.percentage = Math.round((this.selectedItem.validated / this.selectedItem.total) * 100);\r\n      }\r\n    },\r\n\r\n    validateAllItems() {\r\n      if (!this.bulkValidationRootCause) {\r\n        alert(\"Please select a root cause before validating all items.\");\r\n        return;\r\n      }\r\n\r\n      // Find all unvalidated items\r\n      const unvalidatedItems = this.selectedItemDetails.filter(item => item.status !== 'validated');\r\n\r\n      // Validate each item\r\n      unvalidatedItems.forEach(item => {\r\n        item.status = 'validated';\r\n        item.root_cause_1 = this.bulkValidationRootCause;\r\n      });\r\n\r\n      // Update counts\r\n      if (this.selectedItem) {\r\n        this.selectedItem.validated = this.selectedItem.total;\r\n        this.selectedItem.unvalidated = 0;\r\n        this.selectedItem.percentage = 100;\r\n      }\r\n\r\n      // Clear form\r\n      this.bulkValidationRootCause = '';\r\n      this.bulkValidationComments = '';\r\n    },\r\n\r\n    getValidationTagKind(percentage) {\r\n      if (percentage >= 90) return 'green';\r\n      if (percentage >= 70) return 'teal';\r\n      if (percentage >= 50) return 'blue';\r\n      if (percentage >= 30) return 'purple';\r\n      if (percentage >= 10) return 'magenta';\r\n      return 'red';\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return 'N/A';\r\n\r\n      // If it's already in a readable format, return as is\r\n      if (typeof dateString === 'string' && dateString.includes('/')) {\r\n        return dateString;\r\n      }\r\n\r\n      // Try to parse the date\r\n      try {\r\n        const date = new Date(dateString);\r\n        return date.toLocaleDateString();\r\n      } catch (e) {\r\n        return dateString;\r\n      }\r\n    },\r\n\r\n    viewData() {\r\n      console.log(\"View data clicked\");\r\n      // Implement view data functionality\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import \"../../styles/carbon-utils\";\r\n\r\n/* Main container */\r\n.validations-container {\r\n  min-height: 100vh;\r\n  background-color: #161616;\r\n  color: #f4f4f4;\r\n}\r\n\r\n/* Page header */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n  padding: 2rem 2rem 1rem;\r\n  border-bottom: 1px solid #333333;\r\n}\r\n\r\n.page-title {\r\n  color: #f4f4f4;\r\n  font-size: 1.75rem;\r\n  font-weight: 400;\r\n  margin: 0;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 1rem;\r\n}\r\n\r\n/* Filter bar */\r\n.filter-bar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin: 0 2rem 1.5rem;\r\n  background-color: #262626;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.filter-label {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.search-box {\r\n  flex-grow: 1;\r\n  max-width: 300px;\r\n}\r\n\r\n/* Loading and error states */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 3rem;\r\n  margin: 2rem;\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.loading-text {\r\n  margin-top: 1rem;\r\n  color: #8d8d8d;\r\n}\r\n\r\n.error-container {\r\n  margin: 2rem;\r\n}\r\n\r\n/* Content area */\r\n.validations-content {\r\n  padding: 0 2rem 2rem;\r\n}\r\n\r\n.main-validation-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2rem;\r\n}\r\n\r\n.validation-section {\r\n  background-color: #262626;\r\n  border: 1px solid #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n\r\n  h3 {\r\n    color: #f4f4f4;\r\n    font-size: 1.25rem;\r\n    font-weight: 600;\r\n    margin: 0 0 1.5rem 0;\r\n    padding-bottom: 0.75rem;\r\n    border-bottom: 1px solid #393939;\r\n  }\r\n}\r\n\r\n/* Summary tiles */\r\n.validations-summary {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 1rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.summary-tile {\r\n  background-color: #262626;\r\n  border: 1px solid #333333;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  text-align: center;\r\n}\r\n\r\n.tile-title {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  margin-bottom: 0.5rem;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.16px;\r\n}\r\n\r\n.tile-value {\r\n  color: #f4f4f4;\r\n  font-size: 2rem;\r\n  font-weight: 600;\r\n  margin-bottom: 0.25rem;\r\n\r\n  &.validated {\r\n    color: #42be65;\r\n  }\r\n\r\n  &.unvalidated {\r\n    color: #fa4d56;\r\n  }\r\n}\r\n\r\n.tile-percentage {\r\n  color: #8d8d8d;\r\n  font-size: 0.875rem;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Empty states */\r\n.empty-message {\r\n  color: #8d8d8d;\r\n  text-align: center;\r\n  padding: 2rem;\r\n}\r\n\r\n/* Modal styles */\r\n.validation-details-modal {\r\n  max-width: 1000px;\r\n}\r\n\r\n.modal-content {\r\n  padding: 1.5rem 0;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.modal-section {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.section-title {\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.125rem;\r\n  color: #f4f4f4;\r\n  border-bottom: 1px solid #333333;\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.info-label {\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.375rem;\r\n}\r\n\r\n.info-value {\r\n  font-size: 1rem;\r\n  color: #f4f4f4;\r\n}\r\n\r\n.status-banner {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 1rem;\r\n  border-radius: 8px;\r\n  margin-bottom: 1.5rem;\r\n  background-color: rgba(15, 98, 254, 0.1);\r\n  border: 1px solid rgba(15, 98, 254, 0.3);\r\n}\r\n\r\n.validation-summary {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.validation-label {\r\n  color: #f4f4f4;\r\n  font-weight: 500;\r\n}\r\n\r\n.bulk-validation-form {\r\n  background-color: #262626;\r\n  padding: 1.5rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.form-label {\r\n  display: block;\r\n  font-size: 0.875rem;\r\n  color: #8d8d8d;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.form-actions {\r\n  margin-top: 1.5rem;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.modal-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 1rem;\r\n  margin-top: 2rem;\r\n}\r\n\r\n/* Legacy view styles */\r\n.legacy-view {\r\n  padding: 1rem;\r\n  background-color: #262626;\r\n  border-radius: 8px;\r\n  border: 1px solid #333333;\r\n}\r\n\r\n.chart-page__grid {\r\n  margin-top: $spacing-08;\r\n}\r\n\r\n.progress-section {\r\n  margin-top: 2rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.button-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  gap: 1rem; /* Adjust space between buttons */\r\n}\r\n\r\n.fail-container {\r\n  display: flex;                /* Enable flexbox */\r\n  flex-direction: column;       /* Stack items vertically */\r\n  align-items: center;          /* Center horizontally */\r\n  justify-content: center;      /* Center vertically */\r\n  text-align: center;           /* Center text */\r\n  margin: 20px;                 /* Optional: Add margin for spacing */\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 1024px) {\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .filter-bar {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .filter-group {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .search-box {\r\n    max-width: 100%;\r\n  }\r\n\r\n  .validations-summary {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .header-actions {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>"]}]}