const path = require('path');
const xlsx = require('xlsx');
const fs = require('fs');
const logger = require('../utils/logger');

// Path to the validation data storage file
const VALIDATION_DB_PATH = path.join(__dirname, '../data/validation_classifications.json');

// Ensure the data directory exists
function ensureDataDirectoryExists() {
  const dataDir = path.join(__dirname, '../data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    logger.logInfo('Created data directory for validation classifications', 'validation2.controller');
  }
}

// Initialize the validation database if it doesn't exist
function initValidationDb() {
  ensureDataDirectoryExists();

  if (!fs.existsSync(VALIDATION_DB_PATH)) {
    fs.writeFileSync(VALIDATION_DB_PATH, JSON.stringify({
      classifications: {},
      lastUpdated: new Date().toISOString()
    }));
    logger.logInfo('Initialized validation classifications database', 'validation2.controller');
  }
}

// Read the validation database
function readValidationDb() {
  try {
    initValidationDb();
    const data = fs.readFileSync(VALIDATION_DB_PATH, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    logger.logError(`Error reading validation database: ${error.message}`, error, 'validation2.controller');
    return { classifications: {}, lastUpdated: new Date().toISOString() };
  }
}

// Write to the validation database
function writeValidationDb(data) {
  try {
    ensureDataDirectoryExists();
    data.lastUpdated = new Date().toISOString();
    fs.writeFileSync(VALIDATION_DB_PATH, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    logger.logError(`Error writing to validation database: ${error.message}`, error, 'validation2.controller');
    return false;
  }
}

// Read validation data from JSON file (fallback if Excel not available)
function readValidationExcel() {
  try {
    // Try to read from Excel file (.xls)
    const xlsFilePath = path.join(__dirname, '../excel/validation_data.xls');

    // If .xls file exists, read it
    if (fs.existsSync(xlsFilePath)) {
      try {
        const workbook = xlsx.readFile(xlsFilePath);
        const sheetName = workbook.SheetNames[0]; // Assuming data is in the first sheet
        const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

        logger.logInfo(`Successfully read validation Excel (.xls) file. Found ${sheet.length} rows.`, 'validation2.controller');
        return { data: sheet };
      } catch (xlsError) {
        logger.logError(`Error reading .xls file: ${xlsError.message}. Will try .xlsx fallback.`, xlsError, 'validation2.controller');
        // Continue to .xlsx fallback
      }
    }

    // Try to read from Excel file (.xlsx)
    const xlsxFilePath = path.join(__dirname, '../excel/validation_data.xlsx');

    // If .xlsx file exists, read it
    if (fs.existsSync(xlsxFilePath)) {
      try {
        const workbook = xlsx.readFile(xlsxFilePath);
        const sheetName = workbook.SheetNames[0]; // Assuming data is in the first sheet
        const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

        logger.logInfo(`Successfully read validation Excel (.xlsx) file. Found ${sheet.length} rows.`, 'validation2.controller');
        return { data: sheet };
      } catch (xlsxError) {
        logger.logError(`Error reading .xlsx file: ${xlsxError.message}. Will try JSON fallback.`, xlsxError, 'validation2.controller');
        // Continue to JSON fallback
      }
    }

    // Fallback to JSON file
    const jsonFilePath = path.join(__dirname, '../excel/validation_data.json');

    // Check if JSON file exists
    if (!fs.existsSync(jsonFilePath)) {
      logger.logError(`Validation data file not found at path: ${jsonFilePath}`, null, 'validation2.controller');
      return { error: 'Validation data file not found', data: [] };
    }

    // Read the JSON file
    const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));

    logger.logInfo(`Successfully read validation JSON file. Found ${jsonData.length} rows.`, 'validation2.controller');
    return { data: jsonData };
  } catch (error) {
    logger.logError(`Error reading validation data: ${error.message}`, error, 'validation2.controller');
    return { error: error.message, data: [] };
  }
}

// Helper function to get part numbers for a PQE owner
async function getPQEPartNumbers(pqeOwner) {
  try {
    // Load the breakout_targets Excel file to get breakout groups for this PQE owner
    const targetsFilePath = path.join(__dirname, '../excel/breakout_targets.xlsx');
    if (!fs.existsSync(targetsFilePath)) {
      throw new Error(`Breakout targets file not found at path: ${targetsFilePath}`);
    }

    const targetsWorkbook = xlsx.readFile(targetsFilePath);
    if (targetsWorkbook.SheetNames.length < 2) {
      throw new Error(`Breakout targets Excel file does not have a second sheet for owners data`);
    }

    const ownersSheetName = targetsWorkbook.SheetNames[1]; // Second sheet
    const ownersSheet = xlsx.utils.sheet_to_json(targetsWorkbook.Sheets[ownersSheetName]);

    // Filter to get breakout groups for this PQE owner
    const pqeBreakoutGroups = ownersSheet
      .filter(row => row['PQE Owner'] === pqeOwner)
      .map(row => row['Breakout Group'])
      .filter(group => group && group.trim() !== '');

    if (pqeBreakoutGroups.length === 0) {
      logger.logInfo(`No breakout groups found for PQE owner: ${pqeOwner}`, 'getPQEPartNumbers');
      return [];
    }

    // Load the new_metis_test.xlsx file to get part numbers for these breakout groups
    const metisFilePath = path.join(__dirname, '../excel/new_metis_test.xlsx');
    if (!fs.existsSync(metisFilePath)) {
      throw new Error(`Metis file not found at path: ${metisFilePath}`);
    }

    const metisWorkbook = xlsx.readFile(metisFilePath);
    const metisSheetName = metisWorkbook.SheetNames[0];
    const metisSheet = xlsx.utils.sheet_to_json(metisWorkbook.Sheets[metisSheetName]);

    // Filter rows by the breakout groups for this PQE owner and extract part numbers
    const partNumbers = metisSheet
      .filter(row => pqeBreakoutGroups.includes(row['Full Breakout Name']))
      .map(row => row['Part Number'])
      .filter(pn => pn && pn.toString().trim() !== '')
      .map(pn => pn.toString());

    // Remove duplicates
    const uniquePartNumbers = [...new Set(partNumbers)];

    logger.logInfo(`Found ${uniquePartNumbers.length} unique part numbers for PQE owner ${pqeOwner} across ${pqeBreakoutGroups.length} breakout groups`, 'getPQEPartNumbers');
    return uniquePartNumbers;
  } catch (error) {
    logger.logError(`Error getting part numbers for PQE owner ${pqeOwner}: ${error.message}`, error, 'getPQEPartNumbers');
    return [];
  }
}

// Get validation data with time filter and optional PQE owner filter
exports.get_validation_data = async (req, res) => {
  try {
    const { timeFilter, pqeOwner } = req.body;
    logger.logInfo(`Getting validation data with time filter: ${timeFilter}, PQE owner: ${pqeOwner || 'All'}`, 'get_validation_data');

    // Read the Excel file
    const { error, data } = readValidationExcel();
    if (error) {
      return res.status(500).json({ status: 'error', message: error });
    }

    // If no data was found, return an empty array
    if (!data || data.length === 0) {
      return res.status(200).json({
        status: 'success',
        data: [],
        total: 0
      });
    }

    // Read the classifications database
    const db = readValidationDb();

    // Apply time filter and merge with classifications
    const now = new Date();
    let filteredData = data.filter(row => {
      if (!row.MFSDATE) return false;

      // Try to parse the date
      let mfsDate;
      try {
        mfsDate = new Date(row.MFSDATE);
        if (isNaN(mfsDate.getTime())) {
          // Try alternative date formats
          const parts = row.MFSDATE.split(/[-\/]/);
          if (parts.length === 3) {
            // Try YYYY-MM-DD or MM/DD/YYYY
            if (parts[0].length === 4) {
              mfsDate = new Date(parts[0], parts[1] - 1, parts[2]);
            } else {
              mfsDate = new Date(parts[2], parts[0] - 1, parts[1]);
            }
          }

          if (isNaN(mfsDate.getTime())) {
            return false;
          }
        }
      } catch (e) {
        return false;
      }

      switch (timeFilter) {
        case 'day':
          return mfsDate.toDateString() === now.toDateString();
        case 'week':
          const oneWeekAgo = new Date(now);
          oneWeekAgo.setDate(now.getDate() - 7);
          return mfsDate >= oneWeekAgo;
        case 'month':
        default:
          const oneMonthAgo = new Date(now);
          oneMonthAgo.setMonth(now.getMonth() - 1);
          return mfsDate >= oneMonthAgo;
      }
    }).map(row => {
      // Add classification if available
      const defectId = row.DEFECT_ID?.toString();
      return {
        ...row,
        classification: defectId && db.classifications[defectId] ? db.classifications[defectId] : null
      };
    });

    // Apply PQE owner filter if specified
    if (pqeOwner && pqeOwner !== 'All') {
      try {
        const pqePartNumbers = await getPQEPartNumbers(pqeOwner);
        if (pqePartNumbers.length > 0) {
          filteredData = filteredData.filter(row => {
            const partNum = row.PART_NUM?.toString();
            return partNum && pqePartNumbers.includes(partNum);
          });
          logger.logInfo(`Applied PQE owner filter for ${pqeOwner}: filtered to ${filteredData.length} defects`, 'get_validation_data');
        } else {
          logger.logInfo(`No part numbers found for PQE owner ${pqeOwner}, returning empty result`, 'get_validation_data');
          filteredData = [];
        }
      } catch (error) {
        logger.logError(`Error applying PQE owner filter: ${error.message}`, error, 'get_validation_data');
        // Continue without PQE filter if there's an error
      }
    }

    res.status(200).json({
      status: 'success',
      data: filteredData,
      total: filteredData.length
    });
  } catch (error) {
    logger.logError(`Error in get_validation_data: ${error.message}`, error, 'validation2.controller');
    res.status(500).json({ status: 'error', message: error.message });
  }
};

// Get classification counts for pareto chart
exports.get_classification_counts = (req, res) => {
  try {
    const { timeFilter } = req.body;
    logger.logInfo(`Getting classification counts with time filter: ${timeFilter}`, 'get_classification_counts');

    // Read the Excel file
    const { error, data } = readValidationExcel();
    if (error) {
      return res.status(500).json({ status: 'error', message: error });
    }

    // If no data was found, return empty counts
    if (!data || data.length === 0) {
      return res.status(200).json({
        status: 'success',
        data: {
          Mechanical: 0,
          Functional: 0,
          Unclassified: 0
        },
        total: 0
      });
    }

    // Read the classifications database
    const db = readValidationDb();

    // Apply time filter
    const now = new Date();
    const filteredData = data.filter(row => {
      if (!row.MFSDATE) return false;

      // Try to parse the date
      let mfsDate;
      try {
        mfsDate = new Date(row.MFSDATE);
        if (isNaN(mfsDate.getTime())) {
          // Try alternative date formats
          const parts = row.MFSDATE.split(/[-\/]/);
          if (parts.length === 3) {
            // Try YYYY-MM-DD or MM/DD/YYYY
            if (parts[0].length === 4) {
              mfsDate = new Date(parts[0], parts[1] - 1, parts[2]);
            } else {
              mfsDate = new Date(parts[2], parts[0] - 1, parts[1]);
            }
          }

          if (isNaN(mfsDate.getTime())) {
            return false;
          }
        }
      } catch (e) {
        return false;
      }

      switch (timeFilter) {
        case 'day':
          return mfsDate.toDateString() === now.toDateString();
        case 'week':
          const oneWeekAgo = new Date(now);
          oneWeekAgo.setDate(now.getDate() - 7);
          return mfsDate >= oneWeekAgo;
        case 'month':
        default:
          const oneMonthAgo = new Date(now);
          oneMonthAgo.setMonth(now.getMonth() - 1);
          return mfsDate >= oneMonthAgo;
      }
    });

    // Count classifications
    const counts = {
      Mechanical: 0,
      Functional: 0,
      Unclassified: 0
    };

    filteredData.forEach(row => {
      const defectId = row.DEFECT_ID?.toString();
      if (defectId && db.classifications[defectId]) {
        counts[db.classifications[defectId]]++;
      } else {
        counts.Unclassified++;
      }
    });

    res.status(200).json({
      status: 'success',
      data: counts,
      total: filteredData.length
    });
  } catch (error) {
    logger.logError(`Error in get_classification_counts: ${error.message}`, error, 'validation2.controller');
    res.status(500).json({ status: 'error', message: error.message });
  }
};

// Process new defects with WatsonX.ai
exports.process_new_defects = async (req, res) => {
  try {
    const { api_key, project_id, timeFilter } = req.body;
    logger.logInfo(`Processing new defects with WatsonX.ai for time period: ${timeFilter || 'all'}`, 'process_new_defects');

    // Read the Excel file
    const { error, data } = readValidationExcel();
    if (error) {
      return res.status(500).json({ status: 'error', message: error });
    }

    // Read the classifications database
    const db = readValidationDb();

    // Apply time filter if specified
    let timeFilteredData = data;
    if (timeFilter && timeFilter !== 'all') {
      const now = new Date();
      timeFilteredData = data.filter(row => {
        if (!row.MFSDATE) return false;

        // Try to parse the date
        let mfsDate;
        try {
          mfsDate = new Date(row.MFSDATE);
          if (isNaN(mfsDate.getTime())) {
            // Try alternative date formats
            const parts = row.MFSDATE.split(/[-\/]/);
            if (parts.length === 3) {
              // Try YYYY-MM-DD or MM/DD/YYYY
              if (parts[0].length === 4) {
                mfsDate = new Date(parts[0], parts[1] - 1, parts[2]);
              } else {
                mfsDate = new Date(parts[2], parts[0] - 1, parts[1]);
              }
            }

            if (isNaN(mfsDate.getTime())) {
              return false;
            }
          }
        } catch (e) {
          return false;
        }

        switch (timeFilter) {
          case 'day':
            return mfsDate.toDateString() === now.toDateString();
          case 'week':
            const oneWeekAgo = new Date(now);
            oneWeekAgo.setDate(now.getDate() - 7);
            return mfsDate >= oneWeekAgo;
          case 'month':
            const oneMonthAgo = new Date(now);
            oneMonthAgo.setMonth(now.getMonth() - 1);
            return mfsDate >= oneMonthAgo;
          default:
            return true;
        }
      });

      logger.logInfo(`Applied time filter '${timeFilter}': filtered from ${data.length} to ${timeFilteredData.length} defects`, 'process_new_defects');
    }

    // Find only unclassified defects with descriptions
    const unclassifiedDefects = timeFilteredData.filter(row => {
      const defectId = row.DEFECT_ID?.toString();
      return defectId && !db.classifications[defectId] && row.DESCRIPTION;
    });

    if (unclassifiedDefects.length === 0) {
      return res.status(200).json({
        status: 'success',
        message: `No new defects to process for time period: ${timeFilter || 'all'}`,
        processed: 0
      });
    }

    try {
      // Get IAM token to validate API key before proceeding
      const axios = require('axios');
      const iamUrl = 'https://iam.cloud.ibm.com/identity/token';
      const iamData = new URLSearchParams();
      iamData.append('grant_type', 'urn:ibm:params:oauth:grant-type:apikey');
      iamData.append('apikey', api_key);

      const iamHeaders = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      };

      const iamResponse = await axios.post(iamUrl, iamData, { headers: iamHeaders });

      if (!iamResponse.data || !iamResponse.data.access_token) {
        logger.logError('Failed to get IAM token', iamResponse.data, 'process_new_defects');
        return res.status(500).json({
          status: 'error',
          message: 'Failed to authenticate with IBM Cloud. Check your API key.'
        });
      }

      // We'll process these asynchronously and return immediately
      res.status(200).json({
        status: 'success',
        message: `Processing ${unclassifiedDefects.length} new defects with WatsonX.ai for time period: ${timeFilter || 'all'}`,
        processing: unclassifiedDefects.length
      });

      // Process new defects with WatsonX.ai (in the background)
      // Pass an empty array for existingDefects since we're only processing new ones
      processDefectsWithWatsonX(unclassifiedDefects, [], api_key, project_id);
    } catch (error) {
      logger.logError(`Error validating WatsonX API key: ${error.message}`, error, 'process_new_defects');
      return res.status(500).json({
        status: 'error',
        message: `Error validating WatsonX API key: ${error.message}`
      });
    }
  } catch (error) {
    logger.logError(`Error in process_new_defects: ${error.message}`, error, 'validation2.controller');
    res.status(500).json({ status: 'error', message: error.message });
  }
};

// Get part numbers for a specific PQE owner
exports.get_pqe_part_numbers = async (req, res) => {
  try {
    const { pqeOwner } = req.body;
    logger.logInfo(`Getting part numbers for PQE owner: ${pqeOwner}`, 'get_pqe_part_numbers');

    if (!pqeOwner || pqeOwner === 'All') {
      return res.status(400).json({
        status: 'error',
        message: 'PQE owner is required and cannot be "All"'
      });
    }

    const partNumbers = await getPQEPartNumbers(pqeOwner);

    res.status(200).json({
      status: 'success',
      partNumbers: partNumbers,
      total: partNumbers.length
    });
  } catch (error) {
    logger.logError(`Error in get_pqe_part_numbers: ${error.message}`, error, 'get_pqe_part_numbers');
    res.status(500).json({ status: 'error', message: error.message });
  }
};

// Query defects by classification categories
exports.query_by_classification = (req, res) => {
  try {
    const { timeFilter, primaryFilter, subcategoryFilter } = req.body;
    logger.logInfo(`Querying defects by classification: primary=${primaryFilter}, subcategory=${subcategoryFilter}, time=${timeFilter}`, 'query_by_classification');

    // Read the Excel file
    const { error, data } = readValidationExcel();
    if (error) {
      return res.status(500).json({ status: 'error', message: error });
    }

    // Read the classifications database
    const db = readValidationDb();

    // Apply time filter
    const now = new Date();
    let filteredData = data.filter(row => {
      if (!row.MFSDATE) return false;

      // Try to parse the date
      let mfsDate;
      try {
        mfsDate = new Date(row.MFSDATE);
        if (isNaN(mfsDate.getTime())) {
          // Try alternative date formats
          const parts = row.MFSDATE.split(/[-\/]/);
          if (parts.length === 3) {
            // Try YYYY-MM-DD or MM/DD/YYYY
            if (parts[0].length === 4) {
              mfsDate = new Date(parts[0], parts[1] - 1, parts[2]);
            } else {
              mfsDate = new Date(parts[2], parts[0] - 1, parts[1]);
            }
          }

          if (isNaN(mfsDate.getTime())) {
            return false;
          }
        }
      } catch (e) {
        return false;
      }

      switch (timeFilter) {
        case 'day':
          return mfsDate.toDateString() === now.toDateString();
        case 'week':
          const oneWeekAgo = new Date(now);
          oneWeekAgo.setDate(now.getDate() - 7);
          return mfsDate >= oneWeekAgo;
        case 'month':
        default:
          const oneMonthAgo = new Date(now);
          oneMonthAgo.setMonth(now.getMonth() - 1);
          return mfsDate >= oneMonthAgo;
      }
    });

    // Add classification data to each row
    filteredData = filteredData.map(row => {
      const defectId = row.DEFECT_ID?.toString();
      return {
        ...row,
        classification: defectId && db.classifications[defectId] ? db.classifications[defectId] : null
      };
    });

    // Apply classification filters
    if (primaryFilter !== 'all' || subcategoryFilter !== 'all') {
      filteredData = filteredData.filter(row => {
        // Handle missing classification
        if (!row.classification) {
          return (primaryFilter === 'all' || primaryFilter === 'Need More Info') &&
                 (subcategoryFilter === 'all' || subcategoryFilter === 'Need More Info');
        }

        // Handle string classification (backward compatibility)
        if (typeof row.classification === 'string') {
          const primary = row.classification;
          return (primaryFilter === 'all' || primaryFilter === primary) &&
                 (subcategoryFilter === 'all' || subcategoryFilter === 'Other');
        }

        // Handle object classification
        const primary = row.classification.primary || 'Need More Info';
        const subcategory = row.classification.subcategory || 'Need More Info';

        return (primaryFilter === 'all' || primaryFilter === primary) &&
               (subcategoryFilter === 'all' || subcategoryFilter === subcategory);
      });
    }

    res.status(200).json({
      status: 'success',
      data: filteredData,
      total: filteredData.length
    });
  } catch (error) {
    logger.logError(`Error in query_by_classification: ${error.message}`, error, 'validation2.controller');
    res.status(500).json({ status: 'error', message: error.message });
  }
};

// Process a limited batch of defects and return results immediately
exports.process_limited_batch = async (req, res) => {
  try {
    const { api_key, project_id, timeFilter, batchSize = 3 } = req.body;
    logger.logInfo(`Processing limited batch of ${batchSize} defects with WatsonX.ai for time period: ${timeFilter || 'all'}`, 'process_limited_batch');

    // Read the Excel file
    const { error, data } = readValidationExcel();
    if (error) {
      return res.status(500).json({ status: 'error', message: error });
    }

    // Read the classifications database
    const db = readValidationDb();

    // Apply time filter if specified
    let timeFilteredData = data;
    if (timeFilter && timeFilter !== 'all') {
      const now = new Date();
      timeFilteredData = data.filter(row => {
        if (!row.MFSDATE) return false;

        // Try to parse the date
        let mfsDate;
        try {
          mfsDate = new Date(row.MFSDATE);
          if (isNaN(mfsDate.getTime())) {
            // Try alternative date formats
            const parts = row.MFSDATE.split(/[-\/]/);
            if (parts.length === 3) {
              // Try YYYY-MM-DD or MM/DD/YYYY
              if (parts[0].length === 4) {
                mfsDate = new Date(parts[0], parts[1] - 1, parts[2]);
              } else {
                mfsDate = new Date(parts[2], parts[0] - 1, parts[1]);
              }
            }

            if (isNaN(mfsDate.getTime())) {
              return false;
            }
          }
        } catch (e) {
          return false;
        }

        switch (timeFilter) {
          case 'day':
            return mfsDate.toDateString() === now.toDateString();
          case 'week':
            const oneWeekAgo = new Date(now);
            oneWeekAgo.setDate(now.getDate() - 7);
            return mfsDate >= oneWeekAgo;
          case 'month':
            const oneMonthAgo = new Date(now);
            oneMonthAgo.setMonth(now.getMonth() - 1);
            return mfsDate >= oneMonthAgo;
          default:
            return true;
        }
      });

      logger.logInfo(`Applied time filter '${timeFilter}': filtered from ${data.length} to ${timeFilteredData.length} defects`, 'process_limited_batch');
    }

    // Find only unclassified defects with descriptions
    const unclassifiedDefects = timeFilteredData.filter(row => {
      const defectId = row.DEFECT_ID?.toString();
      return defectId && !db.classifications[defectId] && row.DESCRIPTION;
    });

    if (unclassifiedDefects.length === 0) {
      return res.status(200).json({
        status: 'success',
        message: `No new defects to process for time period: ${timeFilter || 'all'}`,
        processed: 0,
        results: []
      });
    }

    // Limit to the requested batch size
    const limitedBatch = unclassifiedDefects.slice(0, batchSize);

    try {
      // Get IAM token to validate API key before proceeding
      console.log("\n\n==== IBM CLOUD IAM TOKEN REQUEST (LIMITED BATCH) ====");
      console.log("Requesting IAM token for WatsonX.ai API access");
      console.log("API Key (masked):", api_key.substring(0, 5) + "..." + api_key.substring(api_key.length - 5));
      console.log("==== END IBM CLOUD IAM TOKEN REQUEST ====\n\n");

      const axios = require('axios');
      const iamUrl = 'https://iam.cloud.ibm.com/identity/token';
      const iamData = new URLSearchParams();
      iamData.append('grant_type', 'urn:ibm:params:oauth:grant-type:apikey');
      iamData.append('apikey', api_key);

      const iamHeaders = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      };

      try {
        const iamResponse = await axios.post(iamUrl, iamData, { headers: iamHeaders });

        console.log("\n\n==== IBM CLOUD IAM TOKEN RESPONSE (LIMITED BATCH) ====");
        console.log("IAM token received successfully");
        console.log("Token type:", iamResponse.data.token_type);
        console.log("Expires in:", iamResponse.data.expires_in, "seconds");
        console.log("Token (first 10 chars):", iamResponse.data.access_token.substring(0, 10) + "...");
        console.log("==== END IBM CLOUD IAM TOKEN RESPONSE ====\n\n");

        if (!iamResponse.data || !iamResponse.data.access_token) {
          logger.logError('Failed to get IAM token', iamResponse.data, 'process_limited_batch');
          return res.status(500).json({
            status: 'error',
            message: 'Failed to authenticate with IBM Cloud. Check your API key.'
          });
        }

        const accessToken = iamResponse.data.access_token;

        // Prepare batch for WatsonX.ai
        const batchDescriptions = limitedBatch.map(item => ({
          id: item.DEFECT_ID.toString(),
          description: item.DESCRIPTION
        }));

        // Log that we're starting batch classification
        console.log("\n\n==== STARTING LIMITED BATCH CLASSIFICATION ====");
        console.log(`Processing ${batchDescriptions.length} defects in a limited batch`);
        console.log("==== END STARTING LIMITED BATCH CLASSIFICATION ====\n\n");

        // Classify the batch
        const classifications = await classifyDefectBatchWithWatsonX(batchDescriptions, accessToken, project_id);

        if (!classifications) {
          return res.status(500).json({
            status: 'error',
            message: 'Failed to classify defects with WatsonX.ai'
          });
        }

        // Process and save the results
        const results = [];
        for (let i = 0; i < limitedBatch.length; i++) {
          const defect = limitedBatch[i];
          const defectId = defect.DEFECT_ID.toString();
          const classification = classifications[i] || { primary: "Need More Info", subcategory: "Unknown" };

          // Save to database
          db.classifications[defectId] = classification;

          // Add to results
          results.push({
            defectId,
            description: defect.DESCRIPTION,
            classification
          });
        }

        // Write the updated classifications back to the database
        writeValidationDb(db);

        // Log the results
        console.log("\n\n==== LIMITED BATCH RESULTS ====");
        console.log(`Successfully processed ${results.length} defects`);
        console.log("Results:");
        results.forEach((result, index) => {
          console.log(`${index + 1}. ID: ${result.defectId} - ${result.classification.primary}/${result.classification.subcategory}`);
        });
        console.log("==== END LIMITED BATCH RESULTS ====\n\n");

        // Return the results immediately
        return res.status(200).json({
          status: 'success',
          message: `Successfully processed ${results.length} defects`,
          processed: results.length,
          results
        });
      } catch (iamError) {
        console.log("\n\n==== IBM CLOUD IAM TOKEN ERROR (LIMITED BATCH) ====");
        console.log("Failed to get IAM token");
        console.log("Error message:", iamError.message);
        if (iamError.response) {
          console.log("Status code:", iamError.response.status);
          console.log("Response data:", JSON.stringify(iamError.response.data, null, 2));
        }
        console.log("Full error object:");
        console.log(iamError);
        console.log("==== END IBM CLOUD IAM TOKEN ERROR ====\n\n");

        logger.logError(`Failed to get IAM token: ${iamError.message}`, iamError, 'process_limited_batch');
        return res.status(500).json({
          status: 'error',
          message: `Failed to authenticate with IBM Cloud: ${iamError.message}`
        });
      }

    } catch (error) {
      logger.logError(`Error processing limited batch: ${error.message}`, error, 'process_limited_batch');
      return res.status(500).json({
        status: 'error',
        message: `Error processing limited batch: ${error.message}`
      });
    }
  } catch (error) {
    logger.logError(`Error in process_limited_batch: ${error.message}`, error, 'validation2.controller');
    res.status(500).json({ status: 'error', message: error.message });
  }
};

// Update classification for a defect
exports.update_classification = (req, res) => {
  try {
    const { defectId, classification } = req.body;
    logger.logInfo(`Updating classification for defect ${defectId} to ${classification}`, 'update_classification');

    if (!defectId || !classification) {
      return res.status(400).json({
        status: 'error',
        message: 'Defect ID and classification are required'
      });
    }

    // Read the classifications database
    const db = readValidationDb();

    // Update the classification
    db.classifications[defectId] = classification;

    // Write back to the database
    const success = writeValidationDb(db);

    if (success) {
      res.status(200).json({
        status: 'success',
        message: `Classification updated for defect ${defectId}`
      });
    } else {
      res.status(500).json({
        status: 'error',
        message: 'Failed to update classification'
      });
    }
  } catch (error) {
    logger.logError(`Error in update_classification: ${error.message}`, error, 'validation2.controller');
    res.status(500).json({ status: 'error', message: error.message });
  }
};

// Helper function to process defects with WatsonX.ai using batch processing
async function processDefectsWithWatsonX(unclassifiedDefects, _existingDefects, api_key, project_id) {
  const axios = require('axios');

  // Read the classifications database
  const db = readValidationDb();
  let newProcessedCount = 0;
  let existingProcessedCount = 0;

  // Get IAM token once for all requests
  let accessToken;
  try {
    // First, get an IAM token using the API key
    console.log("\n\n==== IBM CLOUD IAM TOKEN REQUEST ====");
    console.log("Requesting IAM token for WatsonX.ai API access");
    console.log("API Key (masked):", api_key.substring(0, 5) + "..." + api_key.substring(api_key.length - 5));
    console.log("==== END IBM CLOUD IAM TOKEN REQUEST ====\n\n");

    const iamUrl = 'https://iam.cloud.ibm.com/identity/token';
    const iamData = new URLSearchParams();
    iamData.append('grant_type', 'urn:ibm:params:oauth:grant-type:apikey');
    iamData.append('apikey', api_key);

    const iamHeaders = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json'
    };

    const iamResponse = await axios.post(iamUrl, iamData, { headers: iamHeaders });

    console.log("\n\n==== IBM CLOUD IAM TOKEN RESPONSE ====");
    console.log("IAM token received successfully");
    console.log("Token type:", iamResponse.data.token_type);
    console.log("Expires in:", iamResponse.data.expires_in, "seconds");
    console.log("Token (first 10 chars):", iamResponse.data.access_token.substring(0, 10) + "...");
    console.log("==== END IBM CLOUD IAM TOKEN RESPONSE ====\n\n");

    if (!iamResponse.data || !iamResponse.data.access_token) {
      logger.logError('Failed to get IAM token', iamResponse.data, 'processDefectsWithWatsonX');
      return;
    }

    accessToken = iamResponse.data.access_token;
  } catch (error) {
    console.log("\n\n==== IBM CLOUD IAM TOKEN ERROR ====");
    console.log("Failed to get IAM token");
    console.log("Error message:", error.message);
    if (error.response) {
      console.log("Status code:", error.response.status);
      console.log("Response data:", JSON.stringify(error.response.data, null, 2));
    }
    console.log("Full error object:");
    console.log(error);
    console.log("==== END IBM CLOUD IAM TOKEN ERROR ====\n\n");

    logger.logError(`Error getting IAM token: ${error.message}`, error, 'processDefectsWithWatsonX');
    return;
  }

  // Process defects in smaller batches to avoid truncation issues
  const BATCH_SIZE = 3; // Process 3 defects at a time to avoid truncation issues

  // Prepare only unclassified defects for processing
  const allDefects = unclassifiedDefects
    .map(d => ({
      defect: d,
      isNew: true,
      currentClassification: null
    }))
    .filter(item => item.defect.DESCRIPTION); // Filter out defects with no description

  // Log the number of defects to be processed
  logger.logInfo(`Processing ${allDefects.length} new unclassified defects with WatsonX.ai`, 'processDefectsWithWatsonX');

  // Process in batches
  for (let i = 0; i < allDefects.length; i += BATCH_SIZE) {
    const batch = allDefects.slice(i, i + BATCH_SIZE);

    logger.logInfo(`Processing batch ${Math.floor(i/BATCH_SIZE) + 1} of ${Math.ceil(allDefects.length/BATCH_SIZE)} (${batch.length} defects)`, 'processDefectsWithWatsonX');

    try {
      // Prepare batch for WatsonX.ai
      const batchDescriptions = batch.map(item => ({
        id: item.defect.DEFECT_ID.toString(),
        description: item.defect.DESCRIPTION
      }));

      // Classify the batch
      let classifications = await classifyDefectBatchWithWatsonX(batchDescriptions, accessToken, project_id);

      // If batch classification fails, try individual processing
      if (!classifications || classifications.length !== batch.length) {
        logger.logWarning(`Batch classification failed or returned incomplete results. Expected ${batch.length}, got ${classifications ? classifications.length : 0}. Falling back to individual processing.`, 'processDefectsWithWatsonX');

        // Process each defect individually
        classifications = [];
        for (const desc of batchDescriptions) {
          const classification = await classifyDefectWithWatsonX(desc.description, accessToken, project_id);
          classifications.push(classification);

          // Add a small delay between requests
          await new Promise(resolve => setTimeout(resolve, 300));
        }
      }

      // Log the batch results
      console.log("\n\n==== BATCH PROCESSING RESULTS ====");
      console.log(`Processing results for batch ${Math.floor(i/BATCH_SIZE) + 1} of ${Math.ceil(allDefects.length/BATCH_SIZE)}`);

      // Process each classification result
      for (let j = 0; j < batch.length; j++) {
        const item = batch[j];
        const defectId = item.defect.DEFECT_ID.toString();
        const classification = classifications[j];

        if (classification) {
          if (item.isNew) {
            // New defect - always update
            db.classifications[defectId] = classification;
            newProcessedCount++;
            logger.logInfo(`Classified new defect ${defectId} as ${JSON.stringify(classification)}`, 'processDefectsWithWatsonX');
            console.log(`${j + 1}. ID: ${defectId} - ${item.defect.DESCRIPTION.substring(0, 50)}... => ${classification.primary}/${classification.subcategory}`);
          } else {
            // Existing defect - only update if different
            const currentClassification = item.currentClassification;

            // Compare primary and subcategory classifications
            const isPrimaryDifferent = !currentClassification.primary ||
                                       classification.primary !== currentClassification.primary;
            const isSubcategoryDifferent = !currentClassification.subcategory ||
                                           classification.subcategory !== currentClassification.subcategory;

            if (isPrimaryDifferent || isSubcategoryDifferent) {
              db.classifications[defectId] = classification;
              existingProcessedCount++;
              logger.logInfo(`Reclassified existing defect ${defectId} from ${JSON.stringify(currentClassification)} to ${JSON.stringify(classification)}`, 'processDefectsWithWatsonX');
              console.log(`${j + 1}. ID: ${defectId} - Updated: ${currentClassification.primary}/${currentClassification.subcategory} => ${classification.primary}/${classification.subcategory}`);
            } else {
              logger.logInfo(`Classification unchanged for defect ${defectId}: ${JSON.stringify(currentClassification)}`, 'processDefectsWithWatsonX');
              console.log(`${j + 1}. ID: ${defectId} - Unchanged: ${currentClassification.primary}/${currentClassification.subcategory}`);
            }
          }
        } else {
          // If we still don't have a classification, use a default
          db.classifications[defectId] = { primary: "Need More Info", subcategory: "Unknown" };
          newProcessedCount++;
          logger.logWarning(`Using default classification for defect ${defectId} after all attempts failed`, 'processDefectsWithWatsonX');
          console.log(`${j + 1}. ID: ${defectId} - FAILED: Using default "Need More Info/Unknown"`);
        }
      }

      console.log("==== END BATCH PROCESSING RESULTS ====\n\n");

      // Add a small delay between batches to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      logger.logError(`Error processing batch with WatsonX.ai: ${error.message}`, error, 'processDefectsWithWatsonX');

      // Apply default classifications for this batch
      for (let j = 0; j < batch.length; j++) {
        const item = batch[j];
        const defectId = item.defect.DEFECT_ID.toString();

        // Use default classification
        db.classifications[defectId] = { primary: "Need More Info", subcategory: "Unknown" };
        newProcessedCount++;
        logger.logInfo(`Using default classification for defect ${defectId} due to batch processing error`, 'processDefectsWithWatsonX');
      }
    }
  }

  // Write the updated classifications back to the database
  if (newProcessedCount > 0 || existingProcessedCount > 0) {
    writeValidationDb(db);
    logger.logInfo(`Successfully processed ${newProcessedCount} new defects and ${existingProcessedCount} existing defects with WatsonX.ai`, 'processDefectsWithWatsonX');
  }
}

// Helper function to classify a batch of defects with WatsonX.ai
async function classifyDefectBatchWithWatsonX(descriptions, accessToken, project_id) {
  try {
    const axios = require('axios');

    // Call the WatsonX.ai API with the token
    const watsonxUrl = 'https://us-south.ml.cloud.ibm.com/ml/v1/text/generation?version=2023-05-29';

    // Add error handling for the API call
    try {

    // Create the prompt for batch classification with more strict formatting instructions
    const prompt = `
You are a manufacturing quality analyst specializing in defect classification.

Task: Classify the following ${descriptions.length} defect descriptions with two classification levels:
1. Primary classification: "Mechanical" or "Functional"
2. Subcategory classification: Use a descriptive term that best categorizes the defect

Instructions:
1. Mechanical issues include physical problems like bent cables, damage on cards, threading issues, etc.
2. Functional issues include test failures, firmware issues, or problems indicated by REF codes.
3. For subcategories:
   - Be specific and descriptive (e.g., "Cable Damage", "DCM Replacement", "A-Char Issue", "Threading Problem")
   - Use "Unknown" if you cannot determine a specific subcategory with confidence
   - Choose terms that accurately describe the nature of the defect
   - You are NOT limited to predefined subcategories - use the most appropriate term
   - If the defect involves a component replacement, use the component name in the subcategory

CRITICAL FORMATTING INSTRUCTIONS:
- You MUST respond with ONLY a valid JSON array containing EXACTLY ${descriptions.length} classifications
- You MUST classify ALL ${descriptions.length} defects - this is EXTREMELY IMPORTANT
- Your ENTIRE response must be ONLY the JSON array, nothing else
- Do NOT include any explanations, notes, or other text outside the JSON array
- Do NOT use ellipsis (...) in your response
- Do NOT include any markdown formatting, code blocks, or backticks
- Do NOT include any line breaks within the JSON structure
- Each item in the array MUST have both "primary" and "subcategory" fields (no spaces in field names)
- The response MUST be a complete, valid JSON array that can be parsed with JSON.parse()
- The primary field MUST be one of: "Mechanical", "Functional", or "Need More Info"
- The subcategory field should be a descriptive term or "Unknown" if uncertain

IMPORTANT: Your array MUST contain EXACTLY ${descriptions.length} items - one for each defect description.

The JSON array must be in this exact format with ${descriptions.length} items:
[
  {"primary":"Mechanical","subcategory":"Cable Damage"},
  {"primary":"Functional","subcategory":"DCM Replacement"},
  {"primary":"Mechanical","subcategory":"A-Char Issue"},
  {"primary":"Functional","subcategory":"Test Failure"},
  {"primary":"Mechanical","subcategory":"Unknown"}
]

IMPORTANT: Your response should start with '[' and end with ']' with no other characters before or after.
IMPORTANT: Count your items carefully - you MUST provide EXACTLY ${descriptions.length} classifications.
IMPORTANT: Do NOT include the phrase "Complete JSON Response:" in your response - just provide the raw JSON array.

Defect Descriptions:
${descriptions.map((desc, index) => `${index + 1}. ID: ${desc.id} - ${desc.description}`).join('\n')}

Your JSON array (${descriptions.length} items):
`;

    // Request headers with the IAM token
    const watsonxHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    };

    // Request body with optimized parameters for JSON output
    const watsonxData = {
      model_id: 'ibm/granite-13b-instruct-v2', // Use the instruct model which is better for structured outputs
      input: prompt,
      parameters: {
        temperature: 0.2, // Slight temperature to encourage more varied responses
        max_new_tokens: 500, // Increased token limit to ensure complete response
        min_new_tokens: 50, // Lower minimum to avoid forcing too many tokens
        stop_sequences: ["\n\n\n"], // Only stop on triple newlines to avoid cutting off the response
        repetition_penalty: 1.2, // Moderate repetition penalty
        decoding_method: "sample", // Use sampling for more creative responses
        top_p: 0.9, // Higher top_p for more varied outputs
        top_k: 40, // Consider more tokens for more varied outputs
        return_options: {
          input_text: false, // Don't need to return the input text
          input_tokens: false // Don't need to return the input tokens
        }
        // Removed time_limit to allow the model to complete its response
      },
      project_id: project_id
    };

    // Log the full prompt being sent to WatsonX.ai
    console.log("\n\n==== WATSONX.AI BATCH PROMPT ====");
    console.log(prompt);
    console.log("==== END WATSONX.AI BATCH PROMPT ====\n\n");

    // Send request to WatsonX.ai
    const response = await axios.post(watsonxUrl, watsonxData, { headers: watsonxHeaders });

    // Log the full response from WatsonX.ai
    console.log("\n\n==== WATSONX.AI BATCH RESPONSE ====");
    console.log(JSON.stringify(response.data, null, 2));
    console.log("==== END WATSONX.AI BATCH RESPONSE ====\n\n");

    if (response.data && response.data.results && response.data.results.length > 0) {
      let generatedText = response.data.results[0].generated_text.trim();

      // Log the generated text for easier debugging - MAKE THIS VERY PROMINENT
      console.log("\n\n");
      console.log("*******************************************************************");
      console.log("*                   WATSONX.AI GENERATED TEXT                     *");
      console.log("*******************************************************************");
      console.log(generatedText);
      console.log("*******************************************************************");
      console.log("\n\n");

      // Check if the text contains "Output:" and extract only what follows it
      if (generatedText.includes("Output:")) {
        const outputParts = generatedText.split("Output:");
        if (outputParts.length > 1) {
          generatedText = outputParts[1].trim();
          console.log("Found 'Output:' in response, extracting what follows:");
          console.log(generatedText);
        }
      }

      // Check if the text contains "Complete JSON Response:" and extract only what follows it
      if (generatedText.includes("Complete JSON Response:")) {
        const jsonParts = generatedText.split("Complete JSON Response:");
        if (jsonParts.length > 1) {
          generatedText = jsonParts[1].trim();
          console.log("Found 'Complete JSON Response:' in response, extracting what follows:");
          console.log(generatedText);
        }
      }

      try {
        // Check if the response contains ellipsis (...) which indicates truncation
        if (generatedText.includes('...')) {
          logger.logWarning(`WatsonX.ai returned a truncated response: ${generatedText}`, 'classifyDefectBatchWithWatsonX');

          // Try to extract the valid part of the JSON array
          const startBracket = generatedText.indexOf('[');
          const lastBracket = generatedText.lastIndexOf(']');

          // If we have a complete array with [...], try to parse it
          if (startBracket !== -1 && lastBracket !== -1 && startBracket < lastBracket) {
            try {
              const jsonSubstring = generatedText.substring(startBracket, lastBracket + 1);
              logger.logInfo(`Attempting to parse truncated JSON array: ${jsonSubstring}`, 'classifyDefectBatchWithWatsonX');

              // Replace "..." with closing brackets if needed
              const cleanedJson = jsonSubstring.replace(/,\s*\.{3}\s*\]/, ']');

              const partialResults = JSON.parse(cleanedJson);

              if (Array.isArray(partialResults)) {
                logger.logInfo(`Successfully parsed ${partialResults.length} results from truncated response`, 'classifyDefectBatchWithWatsonX');

                // If we have results for all descriptions, use them
                if (partialResults.length === descriptions.length) {
                  return partialResults.map(result => {
                    // Validate each result
                    const primary = result.primary === "Mechanical" || result.primary === "Functional"
                      ? result.primary
                      : "Need More Info";

                    // Accept any subcategory, but ensure it's a string and not empty
                    let subcategory = result.subcategory;
                    if (!subcategory || typeof subcategory !== 'string' || subcategory.trim() === '') {
                      subcategory = "Unknown";
                    }

                    // If subcategory is "Need More Info", change it to "Unknown"
                    if (subcategory === "Need More Info") {
                      subcategory = "Unknown";
                    }

                    return { primary, subcategory };
                  });
                }

                // If we have partial results, use them for the first N descriptions and process the rest individually
                if (partialResults.length > 0) {
                  logger.logInfo(`Using ${partialResults.length} partial results and processing the rest individually`, 'classifyDefectBatchWithWatsonX');

                  const results = [];

                  // Add the partial results we have
                  for (let i = 0; i < partialResults.length; i++) {
                    const result = partialResults[i];
                    const primary = result.primary === "Mechanical" || result.primary === "Functional"
                      ? result.primary
                      : "Need More Info";

                    // Accept any subcategory, but ensure it's a string and not empty
                    let subcategory = result.subcategory;
                    if (!subcategory || typeof subcategory !== 'string' || subcategory.trim() === '') {
                      subcategory = "Unknown";
                    }

                    // If subcategory is "Need More Info", change it to "Unknown"
                    if (subcategory === "Need More Info") {
                      subcategory = "Unknown";
                    }

                    results.push({ primary, subcategory });
                  }

                  // Process the remaining descriptions individually
                  for (let i = partialResults.length; i < descriptions.length; i++) {
                    const classification = await classifyDefectWithWatsonX(descriptions[i].description, accessToken, project_id);
                    results.push(classification);

                    // Add a small delay between requests
                    await new Promise(resolve => setTimeout(resolve, 300));
                  }

                  return results;
                }
              }
            } catch (parseError) {
              logger.logError(`Failed to parse truncated JSON: ${parseError.message}`, parseError, 'classifyDefectBatchWithWatsonX');
              // Continue to individual processing fallback
            }
          }

          // If we couldn't extract valid JSON, process each defect individually
          logger.logInfo(`Falling back to individual processing for this batch`, 'classifyDefectBatchWithWatsonX');

          // Process each defect individually and collect results
          const individualResults = [];
          for (const desc of descriptions) {
            const classification = await classifyDefectWithWatsonX(desc.description, accessToken, project_id);
            individualResults.push(classification || { primary: "Need More Info", subcategory: "Need More Info" });

            // Add a small delay between requests
            await new Promise(resolve => setTimeout(resolve, 300));
          }

          return individualResults;
        }

        // Try to parse the JSON array from the response
        let classifications;
        try {
          // First, try to find a valid JSON array in the response
          const jsonRegex = /\[.*\]/s;
          const jsonMatch = generatedText.match(jsonRegex);

          if (jsonMatch) {
            // If we found a JSON array, try to parse it
            const jsonText = jsonMatch[0];
            console.log("\n\n==== EXTRACTED JSON ARRAY ====");
            console.log(jsonText);
            console.log("==== END EXTRACTED JSON ARRAY ====\n\n");

            try {
              classifications = JSON.parse(jsonText);
              console.log("Successfully parsed extracted JSON array");
            } catch (extractError) {
              console.log(`Error parsing extracted JSON array: ${extractError.message}`);
              // If parsing the extracted JSON fails, try the original text
              classifications = JSON.parse(generatedText);
            }
          } else {
            // If we couldn't find a JSON array, try to parse the original text
            classifications = JSON.parse(generatedText);
          }
        } catch (jsonError) {
          // If parsing fails, try to clean up the response
          console.log("\n\n==== JSON PARSE ERROR ====");
          console.log(`Error: ${jsonError.message}`);
          console.log("Generated text that couldn't be parsed:");
          console.log(generatedText);
          console.log("==== END JSON PARSE ERROR ====\n\n");

          logger.logWarning(`JSON parse error: ${jsonError.message}. Attempting to clean response.`, 'classifyDefectBatchWithWatsonX');

          // Check for the "[ null" pattern which indicates a failed response
          if (generatedText.trim() === "[ null" || generatedText.trim() === "[null" || generatedText.includes("[ null")) {
            logger.logWarning(`Received incomplete "[ null" response. Falling back to individual processing.`, 'classifyDefectBatchWithWatsonX');

            // Return default classifications instead of individual processing
            logger.logError(`Received incomplete "[ null" response. Original prompt: ${prompt}`, null, 'classifyDefectBatchWithWatsonX');
            return descriptions.map(() => ({ primary: "Need More Info", subcategory: "Unknown" }));
          }

          // Try to extract valid JSON by finding the array brackets
          const startBracket = generatedText.indexOf('[');
          const endBracket = generatedText.lastIndexOf(']');

          if (startBracket !== -1) {
            // We found a starting bracket, but the ending bracket might be missing
            let jsonSubstring;

            if (endBracket !== -1 && startBracket < endBracket) {
              // We have both brackets, extract the JSON
              jsonSubstring = generatedText.substring(startBracket, endBracket + 1);
            } else {
              // The ending bracket is missing, try to reconstruct the JSON
              console.log("\n\n==== ATTEMPTING TO RECONSTRUCT TRUNCATED JSON ====");
              console.log("JSON response appears to be truncated. Attempting to fix it.");

              // Extract what we have so far
              jsonSubstring = generatedText.substring(startBracket);

              // Count the number of complete objects by counting opening braces
              const objectCount = (jsonSubstring.match(/\{/g) || []).length;
              console.log(`Found ${objectCount} opening braces in the truncated JSON`);

              // Check if we have the expected number of objects
              if (objectCount === descriptions.length) {
                console.log("The number of objects matches the expected count. Adding closing bracket.");
                jsonSubstring += "]";
              } else {
                // Try to extract the complete objects
                const completeObjectsRegex = /\{[^{}]*\}/g;
                const completeObjects = jsonSubstring.match(completeObjectsRegex) || [];
                console.log(`Found ${completeObjects.length} complete objects in the truncated JSON`);

                if (completeObjects.length > 0) {
                  // Reconstruct the JSON array with the complete objects
                  jsonSubstring = "[" + completeObjects.join(",") + "]";
                  console.log("Reconstructed JSON array with complete objects");
                } else {
                  console.log("Could not extract any complete objects from the truncated JSON");
                }
              }

              console.log("Reconstructed JSON:");
              console.log(jsonSubstring);
              console.log("==== END RECONSTRUCTION ATTEMPT ====\n\n");
            }

            // Check if the JSON substring is just an empty or null array
            if (jsonSubstring.trim() === "[]" || jsonSubstring.trim() === "[null]") {
              logger.logWarning(`Received empty or null array: ${jsonSubstring}. Falling back to individual processing.`, 'classifyDefectBatchWithWatsonX');

              // Fall back to individual processing
              logger.logInfo(`Falling back to individual processing for this batch`, 'classifyDefectBatchWithWatsonX');

              // Process each defect individually and collect results
              const individualResults = [];
              for (const desc of descriptions) {
                const classification = await classifyDefectWithWatsonX(desc.description, accessToken, project_id);
                individualResults.push(classification || { primary: "Need More Info", subcategory: "Unknown" });

                // Add a small delay between requests
                await new Promise(resolve => setTimeout(resolve, 300));
              }

              return individualResults;
            }

            try {
              classifications = JSON.parse(jsonSubstring);
              logger.logInfo(`Successfully extracted valid JSON from response`, 'classifyDefectBatchWithWatsonX');
            } catch (subError) {
              logger.logError(`Failed to extract valid JSON: ${subError.message}`, subError, 'classifyDefectBatchWithWatsonX');
              console.log("\n\n==== JSON PARSE ERROR AFTER RECONSTRUCTION ====");
              console.log(`Error: ${subError.message}`);
              console.log("JSON that couldn't be parsed:");
              console.log(jsonSubstring);
              console.log("==== END JSON PARSE ERROR ====\n\n");

              // Try to extract individual objects and build an array
              try {
                console.log("\n\n==== ATTEMPTING TO EXTRACT INDIVIDUAL OBJECTS ====");

                // First, try to find objects with primary and subcategory fields
                const primaryObjectRegex = /\{\s*"primary"\s*:\s*"[^"]+"\s*,\s*"subcategory"\s*:\s*"[^"]*"\s*\}/g;
                const primaryObjects = generatedText.match(primaryObjectRegex) || [];
                console.log(`Found ${primaryObjects.length} objects with primary/subcategory fields`);

                if (primaryObjects.length > 0) {
                  // Parse each object individually
                  const parsedObjects = [];
                  for (const obj of primaryObjects) {
                    try {
                      parsedObjects.push(JSON.parse(obj));
                    } catch (objError) {
                      console.log(`Error parsing object: ${obj}`);
                    }
                  }

                  if (parsedObjects.length > 0) {
                    console.log(`Successfully parsed ${parsedObjects.length} objects with primary/subcategory fields`);
                    classifications = parsedObjects;
                  } else {
                    console.log("Could not parse any objects with primary/subcategory fields");
                    throw new Error("Could not parse any objects with primary/subcategory fields");
                  }
                } else {
                  // If we couldn't find any objects with primary/subcategory fields, try to extract any valid JSON objects
                  console.log("No objects with primary/subcategory fields found. Trying to extract any valid JSON objects...");

                  // This regex tries to match any JSON object
                  const anyObjectRegex = /\{[^{}]*\}/g;
                  const anyObjects = generatedText.match(anyObjectRegex) || [];
                  console.log(`Found ${anyObjects.length} potential JSON objects`);

                  if (anyObjects.length > 0) {
                    // Parse each object individually and convert to our expected format
                    const parsedObjects = [];
                    for (const obj of anyObjects) {
                      try {
                        const parsedObj = JSON.parse(obj);
                        console.log(`Successfully parsed object: ${obj}`);

                        // Convert to our expected format if needed
                        if (!parsedObj.primary || !parsedObj.subcategory) {
                          // If the object has id and severity, it's likely a different format
                          if (parsedObj.id !== undefined) {
                            console.log(`Converting object with id ${parsedObj.id} to our format`);
                            parsedObjects.push({
                              primary: "Need More Info",
                              subcategory: "Unknown"
                            });
                          }
                          // If the object has parts or rejection_reason, it might be useful
                          else if (parsedObj.parts || parsedObj.rejection_reason) {
                            console.log(`Converting object with parts/rejection_reason to our format`);
                            const primary = "Mechanical"; // Default to Mechanical for parts issues
                            let subcategory = "Unknown";

                            // Try to determine a better subcategory
                            if (parsedObj.parts && Array.isArray(parsedObj.parts)) {
                              if (parsedObj.parts.includes("Pump")) {
                                subcategory = "Pump Issue";
                              }
                            } else if (parsedObj.rejection_reason) {
                              const reason = Array.isArray(parsedObj.rejection_reason)
                                ? parsedObj.rejection_reason.join(" ").toLowerCase()
                                : parsedObj.rejection_reason.toLowerCase();

                              if (reason.includes("pump")) {
                                subcategory = "Pump Issue";
                              } else if (reason.includes("cable")) {
                                subcategory = "Cable Issue";
                              } else if (reason.includes("dcm")) {
                                subcategory = "DCM Issue";
                              }
                            }

                            parsedObjects.push({ primary, subcategory });
                          } else {
                            // Default conversion for unknown objects
                            parsedObjects.push({
                              primary: "Need More Info",
                              subcategory: "Unknown"
                            });
                          }
                        } else {
                          // Object already has the right format
                          parsedObjects.push(parsedObj);
                        }
                      } catch (objError) {
                        console.log(`Error parsing object: ${obj}`);
                      }
                    }

                    if (parsedObjects.length > 0) {
                      console.log(`Successfully converted ${parsedObjects.length} objects to our format`);
                      classifications = parsedObjects;
                    } else {
                      console.log("Could not parse or convert any objects");
                      throw new Error("Could not parse or convert any objects");
                    }
                  } else {
                    console.log("Could not find any valid JSON objects");
                    throw new Error("Could not find any valid JSON objects");
                  }
                }
                console.log("==== END OBJECT EXTRACTION ATTEMPT ====\n\n");
              } catch (extractError) {
                console.log(`Error extracting objects: ${extractError.message}`);

                // Apply pattern-based classifications
                console.log("\n\n==== APPLYING PATTERN-BASED CLASSIFICATIONS FOR PARSE ERROR ====");
                console.log("JSON parse error. Applying pattern-based classifications.");

                return descriptions.map((desc, index) => {
                  const description = desc.description?.toLowerCase() || '';

                  // Check for DCM replacements
                  if (description.includes('dcm') &&
                     (description.includes('replaced') || description.includes('replacement') ||
                      description.includes('rejected'))) {
                    console.log(`Detected DCM replacement for item ${index + 1}: "${desc.description}"`);
                    return { primary: "Functional", subcategory: "DCM Replacement" };
                  }
                  // Check for SEEPROM issues
                  else if (description.includes('seeprom')) {
                    console.log(`Detected SEEPROM issue for item ${index + 1}: "${desc.description}"`);
                    return { primary: "Functional", subcategory: "SEEPROM Issue" };
                  }
                  // Check for A-Char issues
                  else if (description.includes('a-char')) {
                    console.log(`Detected A-Char issue for item ${index + 1}: "${desc.description}"`);
                    return { primary: "Mechanical", subcategory: "A-Char Issue" };
                  }
                  // Check for reject instructions
                  else if (description.includes('reject')) {
                    console.log(`Detected reject instruction for item ${index + 1}: "${desc.description}"`);
                    return { primary: "Functional", subcategory: "Rejection" };
                  }
                  else {
                    console.log(`No specific pattern detected for item ${index + 1}: "${desc.description}"`);
                    return { primary: "Need More Info", subcategory: "Unknown" };
                  }
                });
              }
            }
          } else {
            logger.logError(`Could not find valid JSON array in response: ${generatedText}`, null, 'classifyDefectBatchWithWatsonX');
            logger.logError(`Could not find valid JSON array in response. Original prompt: ${prompt}`, null, 'classifyDefectBatchWithWatsonX');

            console.log("\n\n==== NO JSON ARRAY FOUND ====");
            console.log("Could not find a JSON array in the response. Applying pattern-based classifications.");
            console.log("==== END NO JSON ARRAY FOUND ====\n\n");

            // Apply pattern-based classifications
            return descriptions.map((desc, index) => {
              const description = desc.description?.toLowerCase() || '';

              // Check for DCM replacements
              if (description.includes('dcm') &&
                 (description.includes('replaced') || description.includes('replacement') ||
                  description.includes('rejected'))) {
                console.log(`Detected DCM replacement for item ${index + 1}: "${desc.description}"`);
                return { primary: "Functional", subcategory: "DCM Replacement" };
              }
              // Check for SEEPROM issues
              else if (description.includes('seeprom')) {
                console.log(`Detected SEEPROM issue for item ${index + 1}: "${desc.description}"`);
                return { primary: "Functional", subcategory: "SEEPROM Issue" };
              }
              // Check for A-Char issues
              else if (description.includes('a-char')) {
                console.log(`Detected A-Char issue for item ${index + 1}: "${desc.description}"`);
                return { primary: "Mechanical", subcategory: "A-Char Issue" };
              }
              // Check for reject instructions
              else if (description.includes('reject')) {
                console.log(`Detected reject instruction for item ${index + 1}: "${desc.description}"`);
                return { primary: "Functional", subcategory: "Rejection" };
              }
              else {
                console.log(`No specific pattern detected for item ${index + 1}: "${desc.description}"`);
                return { primary: "Need More Info", subcategory: "Unknown" };
              }
            });
          }
        }

        // Validate the classifications
        if (Array.isArray(classifications)) {
          // Log the classifications for debugging
          logger.logInfo(`Parsed classifications: ${JSON.stringify(classifications)}`, 'classifyDefectBatchWithWatsonX');

          // Check if we received an empty array
          if (classifications.length === 0) {
            console.log("\n\n==== EMPTY ARRAY RESPONSE FROM WATSONX.AI ====");
            console.log("Received empty array []. Applying special handling for defect descriptions.");
            console.log("==== END EMPTY ARRAY RESPONSE ====\n\n");

            // Apply special handling for each description
            return descriptions.map((desc, index) => {
              const description = desc.description?.toLowerCase() || '';

              // Check for cable issues
              if (description.includes('cable')) {
                console.log(`Detected cable issue for item ${index + 1}: "${desc.description}"`);
                return { primary: "Mechanical", subcategory: "Cable Issue" };
              }
              // Check for DCM replacements
              else if (description.includes('dcm') ||
                      (description.includes('replacing') && description.includes('communications'))) {
                console.log(`Detected replacement for item ${index + 1}: "${desc.description}"`);
                return { primary: "Functional", subcategory: "Component Replacement" };
              }
              // Check for test failures
              else if (description.includes('test') && description.includes('fail')) {
                console.log(`Detected test failure for item ${index + 1}: "${desc.description}"`);
                return { primary: "Functional", subcategory: "Test Failure" };
              }
              // Check for A-Char issues
              else if (description.includes('a-char') || description.includes('character builder')) {
                console.log(`Detected A-Char issue for item ${index + 1}: "${desc.description}"`);
                return { primary: "Mechanical", subcategory: "A-Char Issue" };
              }
              // Check for defective parts
              else if (description.includes('defective') || description.includes('cannot use')) {
                console.log(`Detected defective part for item ${index + 1}: "${desc.description}"`);
                return { primary: "Mechanical", subcategory: "Defective Part" };
              }
              else {
                console.log(`No specific pattern detected for item ${index + 1}: "${desc.description}"`);
                return { primary: "Need More Info", subcategory: "Unknown" };
              }
            });
          }

          // Check if we have at least some classifications
          if (classifications.length > 0) {
            // If we don't have enough classifications for all descriptions, log a warning
            if (classifications.length < descriptions.length) {
              logger.logWarning(`Received fewer classifications (${classifications.length}) than descriptions (${descriptions.length})`, 'classifyDefectBatchWithWatsonX');
              console.log(`\n\n==== WATSONX.AI INCOMPLETE RESPONSE ====`);
              console.log(`Expected ${descriptions.length} classifications but received only ${classifications.length}`);
              console.log(`Padding with default classifications for the remaining ${descriptions.length - classifications.length} items`);
              console.log(`==== END WATSONX.AI INCOMPLETE RESPONSE ====\n\n`);

              // Pad the array with default classifications
              while (classifications.length < descriptions.length) {
                classifications.push({ primary: "Need More Info", subcategory: "Unknown" });
              }
            } else if (classifications.length > descriptions.length) {
              // If we have too many classifications, trim the array
              logger.logWarning(`Received more classifications (${classifications.length}) than descriptions (${descriptions.length})`, 'classifyDefectBatchWithWatsonX');
              console.log(`\n\n==== WATSONX.AI EXCESS RESPONSE ====`);
              console.log(`Expected ${descriptions.length} classifications but received ${classifications.length}`);
              console.log(`Trimming excess classifications`);
              console.log(`==== END WATSONX.AI EXCESS RESPONSE ====\n\n`);

              classifications = classifications.slice(0, descriptions.length);
            } else {
              console.log(`\n\n==== WATSONX.AI COMPLETE RESPONSE ====`);
              console.log(`Successfully received all ${descriptions.length} classifications`);
              console.log(`==== END WATSONX.AI COMPLETE RESPONSE ====\n\n`);
            }

            // Validate each classification
            const validClassifications = classifications.map((cls, index) => {
              // Check if cls is null or undefined
              if (!cls) {
                return { primary: "Need More Info", subcategory: "Unknown" };
              }

              // Check if cls has the required properties
              if (!cls.primary || (!cls.subcategory && !cls['sub category'])) {
                logger.logWarning(`Classification missing required properties: ${JSON.stringify(cls)}`, 'classifyDefectBatchWithWatsonX');
                return { primary: "Need More Info", subcategory: "Unknown" };
              }

              // Handle case where the model returns "sub category" instead of "subcategory"
              if (cls['sub category'] && !cls.subcategory) {
                console.log(`Found "sub category" instead of "subcategory" in item: ${JSON.stringify(cls)}`);
                cls.subcategory = cls['sub category'];
              }

              // Special handling for DCM replacements
              const description = descriptions[index]?.description?.toLowerCase() || '';
              if (description.includes('dcm') && (description.includes('replaced') || description.includes('replacement'))) {
                // DCM replacements are typically Functional issues
                console.log(`Detected DCM replacement for item ${index + 1}: "${descriptions[index]?.description}"`);
                return { primary: "Functional", subcategory: "DCM Replacement" };
              }

              // Ensure primary is either Mechanical or Functional
              const primary = cls.primary === "Mechanical" || cls.primary === "Functional"
                ? cls.primary
                : "Need More Info";

              // Accept any subcategory, but ensure it's a string and not empty
              let subcategory = cls.subcategory;
              if (!subcategory || typeof subcategory !== 'string' || subcategory.trim() === '') {
                subcategory = "Unknown";
              }

              // If subcategory is "Need More Info", change it to "Unknown"
              if (subcategory === "Need More Info") {
                subcategory = "Unknown";
              }

              return { primary, subcategory };
            });

            return validClassifications;
          }
        }

        // If we get here, something went wrong with the classifications
        logger.logError(`Invalid batch classification format: ${generatedText}`, null, 'classifyDefectBatchWithWatsonX');

        // Apply special handling for DCM replacements even when parsing fails
        console.log("\n\n==== APPLYING SPECIAL HANDLING FOR FAILED PARSING ====");
        console.log("Checking for DCM replacements in descriptions");

        return descriptions.map((desc, index) => {
          const description = desc.description?.toLowerCase() || '';

          // Check for cable issues
          if (description.includes('cable')) {
            console.log(`Detected cable issue for item ${index + 1}: "${desc.description}"`);
            return { primary: "Mechanical", subcategory: "Cable Issue" };
          }
          // Check for DCM replacements
          else if (description.includes('dcm') ||
                  (description.includes('replacing') && description.includes('communications'))) {
            console.log(`Detected replacement for item ${index + 1}: "${desc.description}"`);
            return { primary: "Functional", subcategory: "Component Replacement" };
          }
          // Check for test failures
          else if (description.includes('test') && description.includes('fail')) {
            console.log(`Detected test failure for item ${index + 1}: "${desc.description}"`);
            return { primary: "Functional", subcategory: "Test Failure" };
          }
          // Check for A-Char issues
          else if (description.includes('a-char') || description.includes('character builder')) {
            console.log(`Detected A-Char issue for item ${index + 1}: "${desc.description}"`);
            return { primary: "Mechanical", subcategory: "A-Char Issue" };
          }
          // Check for defective parts
          else if (description.includes('defective') || description.includes('cannot use')) {
            console.log(`Detected defective part for item ${index + 1}: "${desc.description}"`);
            return { primary: "Mechanical", subcategory: "Defective Part" };
          }
          // Check for pump issues
          else if (description.includes('pump')) {
            console.log(`Detected pump issue for item ${index + 1}: "${desc.description}"`);
            return { primary: "Mechanical", subcategory: "Pump Issue" };
          }
          else {
            console.log(`No specific pattern detected for item ${index + 1}: "${desc.description}"`);
            return { primary: "Need More Info", subcategory: "Unknown" };
          }
        });
      } catch (parseError) {
        logger.logError(`Error parsing batch classification response: ${parseError.message}. Response: ${generatedText}`, parseError, 'classifyDefectBatchWithWatsonX');
        logger.logError(`Original prompt that caused the error: ${prompt}`, null, 'classifyDefectBatchWithWatsonX');

        // Log to console for immediate visibility
        console.log("\n\n==== WATSONX.AI JSON PARSE ERROR ====");
        console.log(`Error message: ${parseError.message}`);
        console.log("Generated text that couldn't be parsed:");
        console.log(generatedText);
        console.log("Full prompt that caused the error:");
        console.log(prompt);
        console.log("==== END WATSONX.AI JSON PARSE ERROR ====\n\n");

        // Apply special handling for DCM replacements even when parsing fails
        console.log("\n\n==== APPLYING SPECIAL HANDLING FOR PARSE ERROR ====");
        console.log(`Parse error: ${parseError.message}`);
        console.log("Checking for DCM replacements in descriptions");

        return descriptions.map((desc, index) => {
          const description = desc.description?.toLowerCase() || '';

          // Check for cable issues
          if (description.includes('cable')) {
            console.log(`Detected cable issue for item ${index + 1}: "${desc.description}"`);
            return { primary: "Mechanical", subcategory: "Cable Issue" };
          }
          // Check for DCM replacements
          else if (description.includes('dcm') ||
                  (description.includes('replacing') && description.includes('communications'))) {
            console.log(`Detected replacement for item ${index + 1}: "${desc.description}"`);
            return { primary: "Functional", subcategory: "Component Replacement" };
          }
          // Check for test failures
          else if (description.includes('test') && description.includes('fail')) {
            console.log(`Detected test failure for item ${index + 1}: "${desc.description}"`);
            return { primary: "Functional", subcategory: "Test Failure" };
          }
          // Check for A-Char issues
          else if (description.includes('a-char') || description.includes('character builder')) {
            console.log(`Detected A-Char issue for item ${index + 1}: "${desc.description}"`);
            return { primary: "Mechanical", subcategory: "A-Char Issue" };
          }
          // Check for defective parts
          else if (description.includes('defective') || description.includes('cannot use')) {
            console.log(`Detected defective part for item ${index + 1}: "${desc.description}"`);
            return { primary: "Mechanical", subcategory: "Defective Part" };
          }
          // Check for pump issues
          else if (description.includes('pump')) {
            console.log(`Detected pump issue for item ${index + 1}: "${desc.description}"`);
            return { primary: "Mechanical", subcategory: "Pump Issue" };
          }
          else {
            console.log(`No specific pattern detected for item ${index + 1}: "${desc.description}"`);
            return { primary: "Need More Info", subcategory: "Unknown" };
          }
        });
      }
    }

    // Return default classifications if we get here
    console.log("\n\n==== APPLYING DEFAULT CLASSIFICATIONS ====");
    console.log("All other methods failed. Applying pattern-based classifications.");

    return descriptions.map((desc, index) => {
      const description = desc.description?.toLowerCase() || '';

      // Check for cable issues
      if (description.includes('cable')) {
        console.log(`Detected cable issue for item ${index + 1}: "${desc.description}"`);
        return { primary: "Mechanical", subcategory: "Cable Issue" };
      }
      // Check for DCM replacements
      else if (description.includes('dcm') ||
              (description.includes('replacing') && description.includes('communications'))) {
        console.log(`Detected replacement for item ${index + 1}: "${desc.description}"`);
        return { primary: "Functional", subcategory: "Component Replacement" };
      }
      // Check for test failures
      else if (description.includes('test') && description.includes('fail')) {
        console.log(`Detected test failure for item ${index + 1}: "${desc.description}"`);
        return { primary: "Functional", subcategory: "Test Failure" };
      }
      // Check for A-Char issues
      else if (description.includes('a-char') || description.includes('character builder')) {
        console.log(`Detected A-Char issue for item ${index + 1}: "${desc.description}"`);
        return { primary: "Mechanical", subcategory: "A-Char Issue" };
      }
      // Check for defective parts
      else if (description.includes('defective') || description.includes('cannot use')) {
        console.log(`Detected defective part for item ${index + 1}: "${desc.description}"`);
        return { primary: "Mechanical", subcategory: "Defective Part" };
      }
      // Check for pump issues
      else if (description.includes('pump')) {
        console.log(`Detected pump issue for item ${index + 1}: "${desc.description}"`);
        return { primary: "Mechanical", subcategory: "Pump Issue" };
      }
      else {
        console.log(`No specific pattern detected for item ${index + 1}: "${desc.description}"`);
        return { primary: "Need More Info", subcategory: "Unknown" };
      }
    });
    } catch (apiError) {
      // Handle API-specific errors (like 404)
      logger.logError(`WatsonX API error: ${apiError.message}`, apiError, 'classifyDefectBatchWithWatsonX');

      // Log the prompt that caused the error
      logger.logError(`Prompt that caused the error: ${prompt}`, null, 'classifyDefectBatchWithWatsonX');

      // Log to console for immediate visibility
      console.log("\n\n==== WATSONX.AI API ERROR ====");
      console.log(`Error message: ${apiError.message}`);
      console.log("Full prompt that caused the error:");
      console.log(prompt);

      if (apiError.response) {
        logger.logError(`WatsonX API status: ${apiError.response.status}, data: ${JSON.stringify(apiError.response.data)}`, null, 'classifyDefectBatchWithWatsonX');

        // Log response details to console
        console.log(`Status code: ${apiError.response.status}`);
        console.log("Response data:");
        console.log(JSON.stringify(apiError.response.data, null, 2));
      } else {
        console.log("No response object available (network error or timeout)");
      }

      // Log the full error object for debugging
      console.log("Full error object:");
      console.log(apiError);
      console.log("==== END WATSONX.AI API ERROR ====\n\n");

      // Apply pattern-based classifications
      console.log("\n\n==== APPLYING PATTERN-BASED CLASSIFICATIONS FOR API ERROR ====");
      console.log("API error occurred. Applying pattern-based classifications.");

      return descriptions.map((desc, index) => {
        const description = desc.description?.toLowerCase() || '';

        // Check for cable issues
        if (description.includes('cable')) {
          console.log(`Detected cable issue for item ${index + 1}: "${desc.description}"`);
          return { primary: "Mechanical", subcategory: "Cable Issue" };
        }
        // Check for DCM replacements
        else if (description.includes('dcm') ||
                (description.includes('replacing') && description.includes('communications'))) {
          console.log(`Detected replacement for item ${index + 1}: "${desc.description}"`);
          return { primary: "Functional", subcategory: "Component Replacement" };
        }
        // Check for test failures
        else if (description.includes('test') && description.includes('fail')) {
          console.log(`Detected test failure for item ${index + 1}: "${desc.description}"`);
          return { primary: "Functional", subcategory: "Test Failure" };
        }
        // Check for A-Char issues
        else if (description.includes('a-char') || description.includes('character builder')) {
          console.log(`Detected A-Char issue for item ${index + 1}: "${desc.description}"`);
          return { primary: "Mechanical", subcategory: "A-Char Issue" };
        }
        // Check for defective parts
        else if (description.includes('defective') || description.includes('cannot use')) {
          console.log(`Detected defective part for item ${index + 1}: "${desc.description}"`);
          return { primary: "Mechanical", subcategory: "Defective Part" };
        }
        // Check for pump issues
        else if (description.includes('pump')) {
          console.log(`Detected pump issue for item ${index + 1}: "${desc.description}"`);
          return { primary: "Mechanical", subcategory: "Pump Issue" };
        }
        else {
          console.log(`No specific pattern detected for item ${index + 1}: "${desc.description}"`);
          return { primary: "Need More Info", subcategory: "Unknown" };
        }
      });
    }
  } catch (error) {
    logger.logError(`Error in classifyDefectBatchWithWatsonX: ${error.message}`, error, 'classifyDefectBatchWithWatsonX');

    // Apply pattern-based classifications
    console.log("\n\n==== APPLYING PATTERN-BASED CLASSIFICATIONS FOR GENERAL ERROR ====");
    console.log(`General error: ${error.message}`);
    console.log("Applying pattern-based classifications.");

    return descriptions.map((desc, index) => {
      const description = desc.description?.toLowerCase() || '';

      // Check for cable issues
      if (description.includes('cable')) {
        console.log(`Detected cable issue for item ${index + 1}: "${desc.description}"`);
        return { primary: "Mechanical", subcategory: "Cable Issue" };
      }
      // Check for DCM replacements
      else if (description.includes('dcm') ||
              (description.includes('replacing') && description.includes('communications'))) {
        console.log(`Detected replacement for item ${index + 1}: "${desc.description}"`);
        return { primary: "Functional", subcategory: "Component Replacement" };
      }
      // Check for test failures
      else if (description.includes('test') && description.includes('fail')) {
        console.log(`Detected test failure for item ${index + 1}: "${desc.description}"`);
        return { primary: "Functional", subcategory: "Test Failure" };
      }
      // Check for A-Char issues
      else if (description.includes('a-char') || description.includes('character builder')) {
        console.log(`Detected A-Char issue for item ${index + 1}: "${desc.description}"`);
        return { primary: "Mechanical", subcategory: "A-Char Issue" };
      }
      // Check for defective parts
      else if (description.includes('defective') || description.includes('cannot use')) {
        console.log(`Detected defective part for item ${index + 1}: "${desc.description}"`);
        return { primary: "Mechanical", subcategory: "Defective Part" };
      }
      // Check for pump issues
      else if (description.includes('pump')) {
        console.log(`Detected pump issue for item ${index + 1}: "${desc.description}"`);
        return { primary: "Mechanical", subcategory: "Pump Issue" };
      }
      else {
        console.log(`No specific pattern detected for item ${index + 1}: "${desc.description}"`);
        return { primary: "Need More Info", subcategory: "Unknown" };
      }
    });
  }
}

// Helper function to classify a single defect with WatsonX.ai (fallback)
async function classifyDefectWithWatsonX(description, accessToken, project_id) {
  try {
    const axios = require('axios');

    // Call the WatsonX.ai API with the token
    const watsonxUrl = 'https://us-south.ml.cloud.ibm.com/ml/v1/text/generation?version=2023-05-29';

    // Add error handling for the API call
    try {

    // Create the prompt for classification with more strict formatting instructions
    const prompt = `
You are a manufacturing quality analyst specializing in defect classification.

Task: Classify the following defect description with two classification levels:
1. Primary classification: "Mechanical" or "Functional"
2. Subcategory classification: Use a descriptive term that best categorizes the defect

Instructions:
1. Mechanical issues include physical problems like bent cables, damage on cards, threading issues, etc.
2. Functional issues include test failures, firmware issues, or problems indicated by REF codes.
3. For subcategories:
   - Be specific and descriptive (e.g., "Cable Damage", "DCM Replacement", "A-Char Issue", "Threading Problem")
   - Use "Unknown" if you cannot determine a specific subcategory with confidence
   - Choose terms that accurately describe the nature of the defect
   - You are NOT limited to predefined subcategories - use the most appropriate term
   - If the defect involves a component replacement, use the component name in the subcategory

CRITICAL FORMATTING INSTRUCTIONS:
- You MUST respond with ONLY a valid JSON object
- Your ENTIRE response must be ONLY the JSON object, nothing else
- Do NOT include any explanations, notes, or other text outside the JSON object
- The JSON object MUST have both "primary" and "subcategory" fields (no spaces in field names)
- Do NOT respond with just a word like "Mechanical" or "Other"
- The response MUST be a complete, valid JSON object that can be parsed with JSON.parse()
- Do NOT include any markdown formatting, code blocks, or backticks
- Do NOT include any line breaks within the JSON structure
- The primary field MUST be one of: "Mechanical", "Functional", or "Need More Info"
- The subcategory field should be a descriptive term or "Unknown" if uncertain

The JSON object must be in this exact format:
{"primary":"Mechanical","subcategory":"Cable Damage"}

IMPORTANT: Your response should start with '{' and end with '}' with no other characters before or after.
IMPORTANT: Do NOT include the phrase "Complete JSON Response:" in your response - just provide the raw JSON object.

Defect Description:
${description}

Your JSON object:
`;

    // Request headers with the IAM token
    const watsonxHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    };

    // Request body with optimized parameters for JSON output
    const watsonxData = {
      model_id: 'ibm/granite-13b-instruct-v2', // Use the instruct model which is better for structured outputs
      input: prompt,
      parameters: {
        temperature: 0.2, // Slight temperature to encourage more varied responses
        max_new_tokens: 2000, // Increased token limit to ensure complete response
        min_new_tokens: 20, // Ensure we get at least a minimal response
        stop_sequences: ["\n\n\n"], // Only stop on triple newlines to avoid cutting off the response
        repetition_penalty: 1.2, // Moderate repetition penalty
        decoding_method: "sample", // Use sampling for more creative responses
        top_p: 0.9, // Higher top_p for more varied outputs
        top_k: 40, // Consider more tokens for more varied outputs
        return_options: {
          input_text: false, // Don't need to return the input text
          input_tokens: false // Don't need to return the input tokens
        }
        // Removed time_limit to allow the model to complete its response
      },
      project_id: project_id
    };

    // Log the full prompt being sent to WatsonX.ai
    console.log("\n\n==== WATSONX.AI SINGLE DEFECT PROMPT ====");
    console.log(prompt);
    console.log("==== END WATSONX.AI SINGLE DEFECT PROMPT ====\n\n");

    // Send request to WatsonX.ai
    const response = await axios.post(watsonxUrl, watsonxData, { headers: watsonxHeaders });

    // Log the full response from WatsonX.ai
    console.log("\n\n==== WATSONX.AI SINGLE DEFECT RESPONSE ====");
    console.log(JSON.stringify(response.data, null, 2));
    console.log("==== END WATSONX.AI SINGLE DEFECT RESPONSE ====\n\n");

    if (response.data && response.data.results && response.data.results.length > 0) {
      let generatedText = response.data.results[0].generated_text.trim();

      // Log the generated text for easier debugging - MAKE THIS VERY PROMINENT
      console.log("\n\n");
      console.log("*******************************************************************");
      console.log("*              WATSONX.AI SINGLE DEFECT GENERATED TEXT            *");
      console.log("*******************************************************************");
      console.log(generatedText);
      console.log("*******************************************************************");
      console.log("\n\n");

      // Check if the text contains "Output:" and extract only what follows it
      if (generatedText.includes("Output:")) {
        const outputParts = generatedText.split("Output:");
        if (outputParts.length > 1) {
          generatedText = outputParts[1].trim();
          console.log("Found 'Output:' in response, extracting what follows:");
          console.log(generatedText);
        }
      }

      // Check if the text contains "Complete JSON Response:" and extract only what follows it
      if (generatedText.includes("Complete JSON Response:")) {
        const jsonParts = generatedText.split("Complete JSON Response:");
        if (jsonParts.length > 1) {
          generatedText = jsonParts[1].trim();
          console.log("Found 'Complete JSON Response:' in response, extracting what follows:");
          console.log(generatedText);
        }
      }

      try {
        // Try to extract JSON from the response
        let jsonText = generatedText;

        // Check if the response is a simple text like "Mechanical" or "Functional"
        if (generatedText.trim() === "Mechanical" || generatedText.trim() === "Functional") {
          logger.logWarning(`Received simple text response: "${generatedText}". Converting to JSON.`, 'classifyDefectWithWatsonX');
          jsonText = `{"primary": "${generatedText.trim()}", "subcategory": "Other"}`;
        }
        // Check if the response is a simple subcategory
        else if (["Scratches", "Bent", "Plugging", "Discolor", "Misalignment", "Need More Info", "Other"].includes(generatedText.trim())) {
          logger.logWarning(`Received simple subcategory response: "${generatedText}". Converting to JSON.`, 'classifyDefectWithWatsonX');
          jsonText = `{"primary": "Need More Info", "subcategory": "${generatedText.trim()}"}`;
        }
        // Check if the response is in a numbered list format (e.g., "1. Mechanical\n2. Bent")
        else if (generatedText.match(/^\d+\.\s+(Mechanical|Functional)/i)) {
          logger.logWarning(`Received numbered list response: "${generatedText}". Converting to JSON.`, 'classifyDefectWithWatsonX');

          const lines = generatedText.split('\n');
          let primary = "Need More Info";
          let subcategory = "Need More Info";

          // Extract primary classification
          const primaryMatch = lines[0].match(/^\d+\.\s+(Mechanical|Functional)/i);
          if (primaryMatch) {
            primary = primaryMatch[1];
          }

          // Extract subcategory if available
          if (lines.length > 1) {
            const subcategoryMatch = lines[1].match(/^\d+\.\s+(Scratches|Bent|Plugging|Discolor|Misalignment|Need More Info|Other)/i);
            if (subcategoryMatch) {
              subcategory = subcategoryMatch[1];
            }
          }

          jsonText = `{"primary": "${primary}", "subcategory": "${subcategory}"}`;
        }
        // Try to find JSON in the response
        else {
          const jsonMatch = generatedText.match(/\{.*\}/s);
          if (jsonMatch) {
            jsonText = jsonMatch[0];
          }
        }

        // Parse the JSON
        const classification = JSON.parse(jsonText);

        // Handle case where the model returns "sub category" instead of "subcategory"
        if (classification['sub category'] && !classification.subcategory) {
          console.log(`Found "sub category" instead of "subcategory" in single defect classification: ${JSON.stringify(classification)}`);
          classification.subcategory = classification['sub category'];
        }

        // Validate the classification
        if (classification) {
          // Special handling for DCM replacements
          const descriptionLower = description.toLowerCase();
          if (descriptionLower.includes('dcm') && (descriptionLower.includes('replaced') || descriptionLower.includes('replacement'))) {
            // DCM replacements are typically Functional issues
            console.log(`Detected DCM replacement: "${description}"`);
            return { primary: "Functional", subcategory: "DCM Replacement" };
          }

          // Ensure primary is either Mechanical or Functional
          const primary = classification.primary === "Mechanical" || classification.primary === "Functional"
            ? classification.primary
            : "Need More Info";

          // Accept any subcategory, but ensure it's a string and not empty
          let subcategory = classification.subcategory;
          if (!subcategory || typeof subcategory !== 'string' || subcategory.trim() === '') {
            subcategory = "Unknown";
          }

          // If subcategory is "Need More Info", change it to "Unknown"
          if (subcategory === "Need More Info") {
            subcategory = "Unknown";
          }

          return { primary, subcategory };
        } else {
          logger.logError(`Invalid classification format: ${generatedText}`, null, 'classifyDefectWithWatsonX');
          return { primary: "Need More Info", subcategory: "Unknown" };
        }
      } catch (parseError) {
        logger.logError(`Error parsing classification response: ${parseError.message}. Response: ${generatedText}`, parseError, 'classifyDefectWithWatsonX');

        // Log to console for immediate visibility
        console.log("\n\n==== WATSONX.AI SINGLE DEFECT JSON PARSE ERROR ====");
        console.log(`Error message: ${parseError.message}`);
        console.log("Generated text that couldn't be parsed:");
        console.log(generatedText);
        console.log("Full prompt that caused the error:");
        console.log(prompt);
        console.log("==== END WATSONX.AI SINGLE DEFECT JSON PARSE ERROR ====\n\n");

        // Last resort: try to extract information from the text
        if (generatedText.includes("Mechanical")) {
          return { primary: "Mechanical", subcategory: "Unknown" };
        } else if (generatedText.includes("Functional")) {
          return { primary: "Functional", subcategory: "Unknown" };
        } else {
          return { primary: "Need More Info", subcategory: "Unknown" };
        }
      }
    }

    return { primary: "Need More Info", subcategory: "Unknown" };
    } catch (apiError) {
      // Handle API-specific errors (like 404)
      logger.logError(`WatsonX API error in single defect classification: ${apiError.message}`, apiError, 'classifyDefectWithWatsonX');

      // Log the prompt that caused the error
      logger.logError(`Prompt that caused the error: ${prompt}`, null, 'classifyDefectWithWatsonX');

      // Log to console for immediate visibility
      console.log("\n\n==== WATSONX.AI SINGLE DEFECT API ERROR ====");
      console.log(`Error message: ${apiError.message}`);
      console.log("Full prompt that caused the error:");
      console.log(prompt);

      if (apiError.response) {
        logger.logError(`WatsonX API status: ${apiError.response.status}, data: ${JSON.stringify(apiError.response.data)}`, null, 'classifyDefectWithWatsonX');

        // Log response details to console
        console.log(`Status code: ${apiError.response.status}`);
        console.log("Response data:");
        console.log(JSON.stringify(apiError.response.data, null, 2));
      } else {
        console.log("No response object available (network error or timeout)");
      }

      // Log the full error object for debugging
      console.log("Full error object:");
      console.log(apiError);
      console.log("==== END WATSONX.AI SINGLE DEFECT API ERROR ====\n\n");

      // Return default classification when API fails
      return { primary: "Need More Info", subcategory: "Unknown" };
    }
  } catch (error) {
    logger.logError(`Error in classifyDefectWithWatsonX: ${error.message}`, error, 'classifyDefectWithWatsonX');
    return { primary: "Need More Info", subcategory: "Unknown" };
  }
}
