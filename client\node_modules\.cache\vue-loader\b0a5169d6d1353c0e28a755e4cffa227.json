{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue?vue&type=template&id=25dba680&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748960511905}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}