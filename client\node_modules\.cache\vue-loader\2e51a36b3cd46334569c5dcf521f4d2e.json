{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue?vue&type=style&index=0&id=924ed4a2&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\RootCauseChart\\RootCauseChart.vue", "mtime": 1748959578563}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmJ1dHRvblthcmlhLWxhYmVsPSJTaG93IGFzIHRhYmxlIl0gewogIGRpc3BsYXk6IG5vbmU7CiAgcG9pbnRlci1ldmVudHM6IG5vbmU7Cn0KZGl2LnRvb2xiYXItY29udHJvbC5ieC0tb3ZlcmZsb3ctbWVudVthcmlhLWxhYmVsPSJTaG93IGFzIHRhYmxlIl0gewogIGRpc3BsYXk6IG5vbmU7Cn0K"}, {"version": 3, "sources": ["RootCauseChart.vue"], "names": [], "mappings": ";AAwGA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RootCauseChart.vue", "sourceRoot": "src/components/RootCauseChart", "sourcesContent": ["\n\n<template>\n  <div ref='chartHolder'>\n  </div>\n</template>\n\n<script>\nimport Vue from 'vue';\nimport '@carbon/charts/styles.css'; // Import Carbon Charts styles\nimport chartsVue from '@carbon/charts-vue';\nimport { StackedBarChart } from '@carbon/charts'; // Import StackedBarChart\n\nVue.use(chartsVue);\n\nexport default {\n  name: 'RootCauseChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: true // Default is loading true\n    },\n    title: {\n      type: String,\n      default: 'Root Cause Categories'\n    },\n    eventType: {\n      type: Function,\n      default: () => {}\n    }\n  },\n  data() {\n    return {\n      options:{\n      title: this.title,\n        axes: {\n          left: {\n            title: 'Fail Rate (%)',\n            mapsTo: 'value',\n            stacked: true,\n            \n          },\n          bottom: {\n            title: 'Month',\n            mapsTo: 'key',\n            scaleType: 'labels'\n          }\n        },\n        legend: {\n          alignment: 'center',\n          enabled: true\n        },\n        toolbar: {\n          enabled: true\n        },\n        tooltip: {\n          enabled: true\n        },\n        height: this.height,\n        theme: 'g100',\n        stacked: true,\n        animations: true,\n        bars: {\n          width: 50\n        },\n        data: {\n          // groupMapsTo: 'group',\n          loading: this.loading\n        }\n      }\n    }\n    },\n  mounted() {\n    const chartHolder = this.$refs.chartHolder;\n    console.log(chartHolder);\n\n    this.chart = new StackedBarChart(chartHolder, {\n      data: this.data,\n      options: this.options,\n    });\n\n    this.chart.services.events.addEventListener('bar-click', (e) => {\n      this.eventType(e);\n    });\n  },\n  watch: {\n    loading(newVal) {\n      // Update loading state dynamically when the prop changes\n      this.options.data.loading = newVal;\n      this.chart.model.setOptions(this.options);\n    },\n    data(newData) {\n        this.chart.model.setData(newData);\n      \n    }\n  },\n};\n</script>\n\n<style>\nbutton[aria-label=\"Show as table\"] {\n  display: none;\n  pointer-events: none;\n}\ndiv.toolbar-control.bx--overflow-menu[aria-label=\"Show as table\"] {\n  display: none;\n}\n</style>\n"]}]}